Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/cache.py", line 103, in lookup
    r = d[key]
        ~^^^^^
  File "/usr/lib/python3/dist-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/tools/lru.py", line 33, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.model.data', <function IrModelData._xmlid_lookup at 0x76f69531a700>, 'stock.view_picking_tree')

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 537, in _tag_root
    f(rec)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 400, in _tag_record
    f_val = self.id_get(f_ref, raise_if_not_found=nodeattr2bool(rec, 'forcecreate', True))
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 519, in id_get
    res = self.model_id_get(id_str, raise_if_not_found)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 525, in model_id_get
    return self.env['ir.model.data']._xmlid_to_res_model_res_id(id_str, raise_if_not_found=raise_if_not_found)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_model.py", line 2246, in _xmlid_to_res_model_res_id
    return self._xmlid_lookup(xmlid)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/tools/cache.py", line 110, in lookup
    value = d[key] = self.method(*args, **kwargs)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_model.py", line 2239, in _xmlid_lookup
    raise ValueError('External ID not found in the system: %s' % xmlid)
ValueError: External ID not found in the system: stock.view_picking_tree

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1962, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 156, in retrying
    result = func()
             ^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1929, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2177, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_http.py", line 333, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/web/controllers/dataset.py", line 42, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/api.py", line 533, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/app_odoo_customize/models/ir_module_module.py", line 65, in button_immediate_upgrade
    return super(IrModuleModule, self).button_immediate_upgrade()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_module.py", line 671, in button_immediate_upgrade
    return self._button_immediate_function(self.env.registry[self._name].button_upgrade)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 228, in load_module_graph
    load_data(env, idref, mode, kind='data', package=package)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 72, in load_data
    tools.convert_file(env, package.name, filename, idref, mode, noupdate, kind)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 608, in convert_file
    convert_xml_import(env, module, fp, idref, mode, noupdate)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 679, in convert_xml_import
    obj.parse(doc.getroot())
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 594, in parse
    self._tag_root(de)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 537, in _tag_root
    f(rec)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 550, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing /mnt/extra-addons/ai_sales_enhancement/views/stock_picking_views.xml:18, somewhere inside
<record id="view_picking_tree_enhanced" model="ir.ui.view">
            <field name="name">stock.picking.tree.enhanced</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='origin']" position="after">
                    <field name="truck_number" optional="show"/>
                    <field name="remarks" optional="show"/>
                </xpath>
            </field>
        </record>