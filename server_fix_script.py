#!/usr/bin/env python3
"""
Server-side fix script for lot compatibility issue
This script can be run directly on the server without Odoo shell
"""

import sys
import os

def run_odoo_fix(database_name):
    """Run the fix using Odoo API directly"""
    
    try:
        # Add Odoo to Python path if needed
        sys.path.append('/usr/lib/python3/dist-packages')
        
        import odoo
        from odoo import api, SUPERUSER_ID
        from odoo.exceptions import ValidationError
        
        print("=== ODOO LOT COMPATIBILITY FIX ===")
        print(f"Database: {database_name}")
        
        # Initialize Odoo
        odoo.tools.config.parse_config(['--database=' + database_name])
        registry = odoo.registry(database_name)
        
        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            
            # Variables
            lot_name = 'J1P25E28009'
            product_name = 'JEERA SINGAPORE 1% [30KG]'
            mo_id = 170
            
            print(f"Looking for product: {product_name}")
            
            # 1. Find the target product
            target_product = env['product.product'].search([('name', '=', product_name)], limit=1)
            
            if not target_product:
                print(f"❌ ERROR: Product '{product_name}' not found")
                return False
            
            print(f"✅ Found product: {target_product.name} (ID: {target_product.id})")
            
            # 2. Enable lot tracking if needed
            if target_product.tracking == 'none':
                print("⚠️  Enabling lot tracking for product...")
                target_product.tracking = 'lot'
                print("✅ Lot tracking enabled")
            
            # 3. Check if compatible lot exists
            print(f"Checking for compatible lot: {lot_name}")
            compatible_lot = env['stock.lot'].search([
                ('name', '=', lot_name),
                ('product_id', '=', target_product.id)
            ], limit=1)
            
            if compatible_lot:
                print(f"✅ Compatible lot already exists: {compatible_lot.name}")
            else:
                print(f"Creating new compatible lot: {lot_name}")
                try:
                    compatible_lot = env['stock.lot'].create({
                        'name': lot_name,
                        'product_id': target_product.id,
                        'company_id': env.company.id,
                    })
                    print(f"✅ Created lot: {compatible_lot.name} (ID: {compatible_lot.id})")
                except Exception as e:
                    print(f"❌ Error creating lot: {str(e)}")
                    return False
            
            # 4. Update Manufacturing Order
            print(f"Updating Manufacturing Order {mo_id}")
            mo = env['mrp.production'].browse(mo_id)
            
            if not mo.exists():
                print(f"❌ Manufacturing Order {mo_id} not found")
                return False
            
            print(f"✅ Found MO: {mo.name} (State: {mo.state})")
            
            # Find moves for the target product
            target_moves = mo.move_raw_ids.filtered(lambda m: m.product_id.id == target_product.id)
            
            if not target_moves:
                print(f"⚠️  No raw material moves found for {target_product.name}")
                print("Available raw materials:")
                for move in mo.move_raw_ids:
                    print(f"  - {move.product_id.name}")
                return False
            
            print(f"Found {len(target_moves)} move(s) for {target_product.name}")
            
            # Update move lines
            updated_lines = 0
            for move in target_moves:
                if move.move_line_ids:
                    for move_line in move.move_line_ids:
                        if not move_line.lot_id or move_line.lot_id.name == lot_name:
                            move_line.lot_id = compatible_lot
                            updated_lines += 1
                            print(f"  ✅ Updated move line {move_line.id}")
                else:
                    # Create new move line if none exists
                    move_line = env['stock.move.line'].create({
                        'move_id': move.id,
                        'product_id': target_product.id,
                        'lot_id': compatible_lot.id,
                        'product_uom_qty': move.product_uom_qty,
                        'qty_done': 0,
                        'location_id': move.location_id.id,
                        'location_dest_id': move.location_dest_id.id,
                    })
                    print(f"  ✅ Created move line {move_line.id}")
                    updated_lines += 1
            
            # Commit the changes
            cr.commit()
            
            print(f"\n🎉 SUCCESS! Updated {updated_lines} move line(s)")
            print(f"✅ Lot {lot_name} is now compatible with {target_product.name}")
            print(f"✅ Manufacturing Order {mo.name} has been updated")
            
            return True
            
    except ImportError as e:
        print(f"❌ ERROR: Cannot import Odoo modules: {str(e)}")
        print("Make sure you're running this on the Odoo server with proper permissions")
        return False
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_odoo_setup():
    """Check if Odoo is properly set up"""
    
    print("=== CHECKING ODOO SETUP ===")
    
    # Check if Odoo is installed
    try:
        import odoo
        print("✅ Odoo is available")
        print(f"Odoo path: {odoo.__file__}")
    except ImportError:
        print("❌ Odoo not found in Python path")
        return False
    
    # Check common Odoo paths
    odoo_paths = [
        '/usr/bin/odoo',
        '/usr/local/bin/odoo',
        '/opt/odoo/odoo-bin',
    ]
    
    for path in odoo_paths:
        if os.path.exists(path):
            print(f"✅ Found Odoo binary: {path}")
            break
    else:
        print("⚠️  Odoo binary not found in common locations")
    
    return True

def main():
    """Main function"""
    
    if len(sys.argv) != 2:
        print("Usage: python3 server_fix_script.py <database_name>")
        print("")
        print("Example: python3 server_fix_script.py myodoo_db")
        print("")
        print("To find your database name, run:")
        print("sudo -u postgres psql -l")
        return
    
    database_name = sys.argv[1]
    
    # Check setup first
    if not check_odoo_setup():
        return
    
    # Run the fix
    success = run_odoo_fix(database_name)
    
    if success:
        print("\n✅ Fix completed successfully!")
        print("You can now try to use the manufacturing order again.")
    else:
        print("\n❌ Fix failed. Please check the errors above.")

if __name__ == "__main__":
    main()
