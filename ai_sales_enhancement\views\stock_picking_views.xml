<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enhanced Stock Picking Form View -->
        <record id="view_picking_form_enhanced" model="ir.ui.view">
            <field name="name">stock.picking.form.enhanced</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="truck_number" placeholder="Enter truck number"/>
                    <field name="remarks" placeholder="Enter any remarks or notes"/>
                </xpath>

                <!-- Add weight summary information -->
                <xpath expr="//field[@name='location_dest_id']" position="after">
                    <group string="Weight Information" col="4" invisible="picking_type_code not in ('outgoing', 'incoming')">
                        <field name="x_total_purchase_weight" string="Total Purchase Weight (kg)" readonly="1" invisible="picking_type_code != 'incoming'"/>
                        <field name="x_weighbridge_weight" string="Weighbridge Weight (kg)" invisible="picking_type_code != 'incoming'"/>
                        <field name="x_total_bags_weight" string="Total Bags Weight (kg)" readonly="1" invisible="picking_type_code != 'incoming'"/>
                        <field name="x_difference_weight" string="Difference Weight (kg)" readonly="1" invisible="picking_type_code != 'incoming'"/>
                    </group>
                </xpath>

                <!-- Add weight information to stock move lines -->
                <xpath expr="//field[@name='move_ids_without_package']/list/field[@name='product_uom_qty']" position="after">
                    <field name="x_product_total_weight" string="Total Weight (kg)" optional="show" readonly="1"/>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Stock Picking List View -->
        <record id="view_picking_tree_enhanced" model="ir.ui.view">
            <field name="name">stock.picking.tree.enhanced</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.vpicktree"/>
            <field name="arch" type="xml">
                <!-- Add truck number after partner_id for better visibility -->
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="truck_number" string="Truck #" optional="show"/>
                </xpath>
                <!-- Add remarks after origin -->
                <xpath expr="//field[@name='origin']" position="after">
                    <field name="remarks" string="Remarks" optional="hide"/>
                </xpath>
                <!-- Add weight information after state -->
                <xpath expr="//field[@name='state']" position="after">
                    <field name="x_total_purchase_weight" string="Purchase Weight (kg)" optional="hide" readonly="1"/>
                    <field name="x_weighbridge_weight" string="Weighbridge (kg)" optional="hide"/>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Stock Picking Search View -->
        <record id="view_picking_search_enhanced" model="ir.ui.view">
            <field name="name">stock.picking.search.enhanced</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_internal_search"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="truck_number" string="Truck Number"/>
                    <field name="remarks" string="Remarks"/>
                </xpath>
                <xpath expr="//group" position="inside">
                    <filter string="Has Truck Number" name="has_truck_number" domain="[('truck_number', '!=', False)]"/>
                    <filter string="Has Remarks" name="has_remarks" domain="[('remarks', '!=', False)]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>


