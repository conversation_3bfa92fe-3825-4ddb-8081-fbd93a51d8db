<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enhanced Stock Picking Form View -->
        <record id="view_picking_form_enhanced" model="ir.ui.view">
            <field name="name">stock.picking.form.enhanced</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="truck_number" placeholder="Enter truck number"/>
                    <field name="remarks" placeholder="Enter any remarks or notes"/>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Stock Picking List View -->
        <record id="view_picking_tree_enhanced" model="ir.ui.view">
            <field name="name">stock.picking.tree.enhanced</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.vpicktree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='origin']" position="after">
                    <field name="truck_number" optional="show"/>
                    <field name="remarks" optional="show"/>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Stock Picking Search View -->
        <record id="view_picking_search_enhanced" model="ir.ui.view">
            <field name="name">stock.picking.search.enhanced</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_internal_search"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="truck_number" string="Truck Number"/>
                    <field name="remarks" string="Remarks"/>
                </xpath>
                <xpath expr="//group" position="inside">
                    <filter string="Has Truck Number" name="has_truck_number" domain="[('truck_number', '!=', False)]"/>
                    <filter string="Has Remarks" name="has_remarks" domain="[('remarks', '!=', False)]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>


