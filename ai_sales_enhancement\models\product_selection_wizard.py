from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class ProductLotSelectionWizard(models.TransientModel):
    _inherit = 'product.lot.selection.wizard'

    # Enhanced fields for weight display
    selected_lot_weight = fields.Float(
        string='Selected Lot Weight (kg)',
        compute='_compute_selected_lot_info',
        help="Total weight of the selected lot"
    )
    
    selected_lot_total_qty = fields.Float(
        string='Selected Lot Total Qty',
        compute='_compute_selected_lot_info',
        help="Total available quantity of the selected lot"
    )

    @api.depends('selected_product_id', 'lot_number')
    def _compute_selected_lot_info(self):
        """Compute weight and quantity information for selected lot"""
        for wizard in self:
            if wizard.selected_product_id and wizard.lot_number:
                # Find the lot
                lot = self.env['stock.lot'].search([
                    ('name', '=', wizard.lot_number),
                    ('product_id', '=', wizard.selected_product_id.id)
                ], limit=1)
                
                if lot:
                    wizard.selected_lot_weight = lot.weight
                    wizard.selected_lot_total_qty = lot.total_available_qty
                else:
                    wizard.selected_lot_weight = 0.0
                    wizard.selected_lot_total_qty = 0.0
            else:
                wizard.selected_lot_weight = 0.0
                wizard.selected_lot_total_qty = 0.0


class ProductSelectionWizard(models.TransientModel):
    _inherit = 'product.selection.wizard'

    def _prepare_wizard_lines(self):
        """Override to include weight information in wizard lines"""
        res = super()._prepare_wizard_lines()
        
        # Enhance the lines with weight information
        for line_vals in res:
            if 'product_id' in line_vals:
                product = self.env['product.product'].browse(line_vals['product_id'])
                
                # Calculate total weight available
                lots = self.env['stock.lot'].search([
                    ('name', '=', self.lot_number),
                    ('product_id', '=', product.id)
                ])
                
                total_weight = sum(lots.mapped('weight'))
                line_vals['total_weight_available'] = total_weight
        
        return res


class ProductSelectionWizardLine(models.TransientModel):
    _inherit = 'product.selection.wizard.line'

    # Enhanced fields for weight display
    total_weight_available = fields.Float(
        string='Total Weight Available (kg)',
        help="Total weight available for this product with the specified lot number"
    )
    
    weight_per_unit = fields.Float(
        string='Weight per Unit (kg)',
        related='product_id.weight',
        readonly=True,
        help="Weight per unit of the product"
    )

    @api.depends('product_id', 'wizard_id.lot_number')
    def _compute_stock_info(self):
        """Override to include weight information in stock details"""
        super()._compute_stock_info()
        
        for line in self:
            if line.product_id and line.wizard_id.lot_number:
                # Find lots for this product with the specified lot number
                lots = self.env['stock.lot'].search([
                    ('name', '=', line.wizard_id.lot_number),
                    ('product_id', '=', line.product_id.id)
                ])
                
                if lots:
                    # Calculate total weight
                    total_weight = sum(lots.mapped('weight'))
                    line.total_weight_available = total_weight
                    
                    # Enhance stock details with weight information
                    stock_details = []
                    for lot in lots:
                        for quant in lot.quant_ids.filtered(lambda q: q.quantity > 0):
                            weight_info = ""
                            if line.product_id.weight:
                                unit_weight = quant.quantity * line.product_id.weight
                                weight_info = f" ({unit_weight:.2f} kg)"
                            
                            stock_details.append(
                                f"{quant.location_id.name}: {quant.quantity:.2f}{weight_info}"
                            )
                    
                    if stock_details:
                        line.stock_details_text = "\n".join(stock_details)
                else:
                    line.total_weight_available = 0.0
