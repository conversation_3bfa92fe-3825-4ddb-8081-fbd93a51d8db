from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class ProductProduct(models.Model):
    _inherit = 'product.product'

    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """Enhanced search to include lot numbers"""
        if args is None:
            args = []
        
        # Normal search first
        results = super(ProductProduct, self).name_search(name, args, operator, limit)
        
        # If searching and we have a search term, also search by lot numbers
        if name:
            # Find lots that match the search term
            lots = self.env['stock.lot'].search([
                ('name', operator, name)
            ], limit=limit)
            
            if lots:
                # Get products from these lots
                lot_products = lots.mapped('product_id')
                lot_results = [(p.id, f"{p.display_name} [Lot: {', '.join(lots.filtered(lambda l: l.product_id.id == p.id).mapped('name'))}]") 
                              for p in lot_products]
                
                # Combine results, avoiding duplicates
                seen_ids = {r[0] for r in results}
                for lot_result in lot_results:
                    if lot_result[0] not in seen_ids:
                        results.append(lot_result)
                        seen_ids.add(lot_result[0])
        
        return results[:limit]

    def get_available_lots_with_stock(self, location_id=None):
        """Get available lots with stock for this product"""
        self.ensure_one()
        
        domain = [
            ('product_id', '=', self.id),
            ('quant_ids.quantity', '>', 0)
        ]
        
        if location_id:
            domain.append(('quant_ids.location_id', '=', location_id))
        
        return self.env['stock.lot'].search(domain)

    def get_stock_summary_by_lot(self):
        """Get stock summary grouped by lot"""
        self.ensure_one()
        
        lots = self.env['stock.lot'].search([('product_id', '=', self.id)])
        summary = []
        
        for lot in lots:
            total_qty = sum(lot.quant_ids.mapped('quantity'))
            if total_qty > 0:
                weight = total_qty * self.weight if self.weight else 0.0
                locations = lot.quant_ids.filtered(lambda q: q.quantity > 0).mapped('location_id.name')
                
                summary.append({
                    'lot_id': lot.id,
                    'lot_name': lot.name,
                    'quantity': total_qty,
                    'weight': weight,
                    'locations': ', '.join(locations),
                })
        
        return summary

    @api.model
    def search_products_by_lot_or_name(self, search_term, limit=10):
        """Search products by lot number or product name"""
        if not search_term:
            return self.browse([])
        
        # Search by product name/code first
        products_by_name = self.search([
            '|',
            ('name', 'ilike', search_term),
            ('default_code', 'ilike', search_term)
        ], limit=limit)
        
        # Search by lot number
        lots = self.env['stock.lot'].search([
            ('name', 'ilike', search_term)
        ], limit=limit)
        
        products_by_lot = lots.mapped('product_id')
        
        # Combine and remove duplicates
        all_products = products_by_name | products_by_lot
        
        return all_products[:limit]


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Enhanced tracking configuration
    allow_negative_stock_sales = fields.Boolean(
        string='Allow Negative Stock Sales',
        help="Allow this product to be sold even with negative stock. "
             "Useful for made-to-order products."
    )
    
    auto_create_lots_on_sale = fields.Boolean(
        string='Auto-create Lots on Sale',
        help="Automatically create lot numbers when selling this product with negative stock."
    )
    
    default_lot_prefix = fields.Char(
        string='Default Lot Prefix',
        help="Default prefix for auto-generated lot numbers for this product."
    )

    @api.onchange('tracking')
    def _onchange_tracking(self):
        """Auto-enable lot creation for tracked products"""
        if self.tracking in ['lot', 'serial']:
            self.auto_create_lots_on_sale = True
