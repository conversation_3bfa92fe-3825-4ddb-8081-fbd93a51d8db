from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError, ValidationError


class TestHamliSortexOverride(TransactionCase):
    
    def setUp(self):
        super(TestHamliSortexOverride, self).setUp()
        
        # Create a test product
        self.product = self.env['product.product'].create({
            'name': 'Test Spice Product',
            'type': 'product',
            'weight': 1.0,
        })
        
        # Create a test manufacturing order
        self.mo = self.env['mrp.production'].create({
            'product_id': self.product.id,
            'product_qty': 100,
            'product_uom_id': self.product.uom_id.id,
            'hamali_rate': 2.0,
            'sortex_rate': 1.5,
        })

    def test_hamli_cost_override_enable_disable(self):
        """Test enabling and disabling hamli cost override"""
        # Initially, override should be disabled
        self.assertFalse(self.mo.hamli_cost_override_mode)
        self.assertEqual(self.mo.hamli_cost_manual, 0.0)

        # Set total weight for calculation (can be done in any state)
        self.mo.total_weight = 100.0

        # Enable hamli override (can be done in any state)
        result = self.mo.action_enable_hamli_override()

        # Check that override is enabled
        self.assertTrue(self.mo.hamli_cost_override_mode)
        self.assertEqual(self.mo.hamli_cost_manual, self.mo.hamali_cost)
        self.assertIsNotNone(self.mo.hamli_cost_last_modified_by)

        # Check notification result
        self.assertEqual(result['type'], 'ir.actions.client')
        self.assertEqual(result['params']['type'], 'success')

        # Disable hamli override
        result = self.mo.action_disable_hamli_override()

        # Check that override is disabled
        self.assertFalse(self.mo.hamli_cost_override_mode)
        self.assertEqual(self.mo.hamli_cost_manual, 0.0)

    def test_sortex_cost_override_enable_disable(self):
        """Test enabling and disabling sortex cost override"""
        # Initially, override should be disabled
        self.assertFalse(self.mo.sortex_cost_override_mode)
        self.assertEqual(self.mo.sortex_cost_manual, 0.0)

        # Set total weight for calculation (can be done in any state)
        self.mo.total_weight = 100.0

        # Enable sortex override (can be done in any state)
        result = self.mo.action_enable_sortex_override()

        # Check that override is enabled
        self.assertTrue(self.mo.sortex_cost_override_mode)
        self.assertEqual(self.mo.sortex_cost_manual, self.mo.sortex_landed_cost)
        self.assertIsNotNone(self.mo.sortex_cost_last_modified_by)

        # Disable sortex override
        result = self.mo.action_disable_sortex_override()

        # Check that override is disabled
        self.assertFalse(self.mo.sortex_cost_override_mode)
        self.assertEqual(self.mo.sortex_cost_manual, 0.0)

    def test_rate_override_calculation(self):
        """Test that rate overrides work correctly"""
        self.mo.total_weight = 100.0

        # Test automatic calculation
        original_hamali = self.mo.hamali_cost
        original_sortex = self.mo.sortex_landed_cost

        # Enable override and set manual rates
        self.mo.hamli_cost_override_mode = True
        self.mo.hamli_rate_manual = 2.5  # 2.5 per kg
        self.mo._compute_hamali_cost()

        self.mo.sortex_cost_override_mode = True
        self.mo.sortex_rate_manual = 1.8  # 1.8 per kg
        self.mo._compute_sortex_landed_cost()

        # Check that costs now use manual rates × total weight
        self.assertEqual(self.mo.hamali_cost, 250.0)  # 2.5 × 100
        self.assertEqual(self.mo.sortex_landed_cost, 180.0)  # 1.8 × 100

        # Disable override
        self.mo.hamli_cost_override_mode = False
        self.mo.sortex_cost_override_mode = False
        self.mo._compute_hamali_cost()
        self.mo._compute_sortex_landed_cost()

        # Check that costs return to automatic calculation
        self.assertEqual(self.mo.hamali_cost, original_hamali)
        self.assertEqual(self.mo.sortex_landed_cost, original_sortex)

    def test_rate_integration(self):
        """Test that overridden rates integrate with other calculations"""
        self.mo.total_weight = 100.0

        # Get original total cost
        original_total_cost = self.mo.total_cost

        # Enable override with higher rate
        self.mo.hamli_cost_override_mode = True
        self.mo.hamli_rate_manual = 3.0  # Higher than default 2.0
        self.mo._compute_hamali_cost()

        # Total cost should increase
        new_total_cost = self.mo.total_cost
        self.assertGreater(new_total_cost, original_total_cost)

        # The difference should be approximately the difference in hamali cost
        cost_difference = new_total_cost - original_total_cost
        hamali_difference = (3.0 * self.mo.total_weight) - (self.mo.hamali_rate * self.mo.total_weight)
        self.assertAlmostEqual(cost_difference, hamali_difference, places=2)

    def test_validation_constraints(self):
        """Test validation constraints for manual rates"""
        self.mo.hamli_cost_override_mode = True

        # Test negative hamli rate validation
        with self.assertRaises(ValidationError):
            self.mo.hamli_rate_manual = -2.0
            self.mo._check_manual_rates()

        # Test negative sortex rate validation
        self.mo.sortex_cost_override_mode = True
        with self.assertRaises(ValidationError):
            self.mo.sortex_rate_manual = -1.5
            self.mo._check_manual_rates()

    def test_production_b_logic(self):
        """Test that Production B logic is respected"""
        self.mo.is_production_b = True

        # Sortex override should not be allowed for Production B
        # This would be enforced in the UI, but we can test the logic
        self.mo.sortex_cost_override_mode = True
        self.mo.sortex_cost_manual = 100.0
        self.mo._compute_final_costs()

        # For Production B, sortex cost should be 0 regardless of override
        # (This depends on the original module's logic)

    def test_rate_recalculation(self):
        """Test rate recalculation functionality"""
        self.mo.total_weight = 100.0

        # Enable overrides
        self.mo.hamli_cost_override_mode = True
        self.mo.hamli_rate_manual = 2.5
        self.mo.sortex_cost_override_mode = True
        self.mo.sortex_rate_manual = 1.8

        # Test recalculation
        result = self.mo.action_recalculate_costs()

        # Check notification result
        self.assertEqual(result['type'], 'ir.actions.client')
        self.assertEqual(result['params']['type'], 'success')
