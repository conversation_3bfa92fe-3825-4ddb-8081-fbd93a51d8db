#!/bin/bash
echo "=== ODOO SERVER SETUP COMMANDS ==="
echo "Run these commands on your server after SSH connection"
echo ""

echo "1. FIND ODOO CONFIGURATION:"
echo "sudo find / -name 'odoo.conf' 2>/dev/null"
echo "# OR"
echo "sudo find /etc -name '*odoo*' 2>/dev/null"
echo ""

echo "2. CHECK ODOO SERVICE STATUS:"
echo "sudo systemctl status odoo"
echo "# OR"
echo "sudo service odoo status"
echo ""

echo "3. FIND ODOO USER:"
echo "ps aux | grep odoo"
echo ""

echo "4. CHECK POSTGRESQL DATABASES:"
echo "sudo -u postgres psql -l"
echo ""

echo "5. FIND ODOO INSTALLATION PATH:"
echo "which odoo"
echo "# OR"
echo "find /usr -name 'odoo*' 2>/dev/null"
echo ""

echo "6. CHECK ODOO ADDONS PATH:"
echo "sudo find / -path '*/extra-addons' 2>/dev/null"
echo ""

echo "7. COPY SCRIPT TO SERVER:"
echo "# If scripts are not on server, copy them:"
echo "scp fix_lot_compatibility.py username@server:/tmp/"
echo "scp quick_fix.py username@server:/tmp/"
echo ""

echo "8. COMMON ODOO SHELL COMMANDS:"
echo "# As odoo user:"
echo "sudo -u odoo python3 /usr/bin/odoo shell -d DATABASE_NAME"
echo ""
echo "# With config file:"
echo "sudo -u odoo python3 /usr/bin/odoo shell -c /etc/odoo/odoo.conf -d DATABASE_NAME"
echo ""
echo "# Direct with postgres user:"
echo "sudo -u postgres python3 /usr/bin/odoo shell -d DATABASE_NAME"
echo ""

echo "=== STEP-BY-STEP EXECUTION ==="
echo ""
echo "After finding your database name and odoo user, run:"
echo ""
echo "# 1. Navigate to the script location"
echo "cd /path/to/your/scripts"
echo ""
echo "# 2. Access Odoo shell (replace DATABASE_NAME with your actual database)"
echo "sudo -u odoo python3 /usr/bin/odoo shell -d DATABASE_NAME"
echo ""
echo "# 3. In Odoo shell, run:"
echo "exec(open('/path/to/quick_fix.py').read())"
echo "quick_fix_lot_issue()"
echo ""

echo "=== ALTERNATIVE: DIRECT EXECUTION ==="
echo ""
echo "If you can't access Odoo shell, create a Python script:"
echo ""
echo "cat > /tmp/run_fix.py << 'EOF'"
echo "import odoo"
echo "from odoo import api, SUPERUSER_ID"
echo ""
echo "# Initialize Odoo"
echo "odoo.tools.config.parse_config(['--database=DATABASE_NAME'])"
echo "registry = odoo.registry('DATABASE_NAME')"
echo ""
echo "with registry.cursor() as cr:"
echo "    env = api.Environment(cr, SUPERUSER_ID, {})"
echo "    "
echo "    # Your fix code here"
echo "    lot_name = 'J1P25E28009'"
echo "    product_name = 'JEERA SINGAPORE 1% [30KG]'"
echo "    "
echo "    target_product = env['product.product'].search([('name', '=', product_name)], limit=1)"
echo "    if target_product:"
echo "        if target_product.tracking == 'none':"
echo "            target_product.tracking = 'lot'"
echo "        "
echo "        compatible_lot = env['stock.lot'].create({"
echo "            'name': lot_name,"
echo "            'product_id': target_product.id,"
echo "            'company_id': env.company.id,"
echo "        })"
echo "        print(f'Created compatible lot: {compatible_lot.name}')"
echo "        "
echo "        # Update MO"
echo "        mo = env['mrp.production'].browse(170)"
echo "        if mo.exists():"
echo "            target_moves = mo.move_raw_ids.filtered(lambda m: m.product_id.id == target_product.id)"
echo "            for move in target_moves:"
echo "                for move_line in move.move_line_ids:"
echo "                    move_line.lot_id = compatible_lot"
echo "            print('Updated manufacturing order')"
echo "    "
echo "    cr.commit()"
echo "EOF"
echo ""
echo "# Then run:"
echo "sudo -u odoo python3 /tmp/run_fix.py"
