from odoo import api, fields, models, _


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    # Negative stock configuration
    allow_negative_stock_sales = fields.Boolean(
        string='Allow Negative Stock in Sales',
        config_parameter='ai_sales_enhancement.allow_negative_stock_sales',
        help="Allow sales of products even when stock is negative. "
             "Useful for made-to-order scenarios where products are sold before manufacturing."
    )
    
    auto_create_lots_negative_stock = fields.Boolean(
        string='Auto-create Lots for Negative Stock',
        config_parameter='ai_sales_enhancement.auto_create_lots_negative_stock',
        help="Automatically create lot numbers when selling products with negative stock."
    )
    
    default_lot_prefix = fields.Char(
        string='Default Lot Prefix',
        config_parameter='ai_sales_enhancement.default_lot_prefix',
        default='LOT',
        help="Default prefix for auto-generated lot numbers."
    )
    
    # Enhanced search configuration
    enable_enhanced_product_search = fields.Boolean(
        string='Enable Enhanced Product Search',
        config_parameter='ai_sales_enhancement.enable_enhanced_product_search',
        default=True,
        help="Enable enhanced product/lot search functionality in sales orders."
    )
    
    auto_fill_location_from_lot = fields.Boolean(
        string='Auto-fill Location from Lot',
        config_parameter='ai_sales_enhancement.auto_fill_location_from_lot',
        default=True,
        help="Automatically fill location when lot is selected based on highest stock location."
    )
    
    # Truck tracking configuration
    enable_truck_tracking = fields.Boolean(
        string='Enable Truck Tracking',
        config_parameter='ai_sales_enhancement.enable_truck_tracking',
        default=True,
        help="Enable truck number tracking for delivery orders."
    )
    
    mandatory_truck_number = fields.Boolean(
        string='Mandatory Truck Number',
        config_parameter='ai_sales_enhancement.mandatory_truck_number',
        help="Make truck number mandatory for delivery orders."
    )

    @api.model
    def get_values(self):
        res = super(ResConfigSettings, self).get_values()
        ICPSudo = self.env['ir.config_parameter'].sudo()
        
        res.update(
            allow_negative_stock_sales=ICPSudo.get_param(
                'ai_sales_enhancement.allow_negative_stock_sales', False
            ),
            auto_create_lots_negative_stock=ICPSudo.get_param(
                'ai_sales_enhancement.auto_create_lots_negative_stock', False
            ),
            default_lot_prefix=ICPSudo.get_param(
                'ai_sales_enhancement.default_lot_prefix', 'LOT'
            ),
            enable_enhanced_product_search=ICPSudo.get_param(
                'ai_sales_enhancement.enable_enhanced_product_search', True
            ),
            auto_fill_location_from_lot=ICPSudo.get_param(
                'ai_sales_enhancement.auto_fill_location_from_lot', True
            ),
            enable_truck_tracking=ICPSudo.get_param(
                'ai_sales_enhancement.enable_truck_tracking', True
            ),
            mandatory_truck_number=ICPSudo.get_param(
                'ai_sales_enhancement.mandatory_truck_number', False
            ),
        )
        return res

    def set_values(self):
        super(ResConfigSettings, self).set_values()
        ICPSudo = self.env['ir.config_parameter'].sudo()
        
        ICPSudo.set_param(
            'ai_sales_enhancement.allow_negative_stock_sales',
            self.allow_negative_stock_sales
        )
        ICPSudo.set_param(
            'ai_sales_enhancement.auto_create_lots_negative_stock',
            self.auto_create_lots_negative_stock
        )
        ICPSudo.set_param(
            'ai_sales_enhancement.default_lot_prefix',
            self.default_lot_prefix
        )
        ICPSudo.set_param(
            'ai_sales_enhancement.enable_enhanced_product_search',
            self.enable_enhanced_product_search
        )
        ICPSudo.set_param(
            'ai_sales_enhancement.auto_fill_location_from_lot',
            self.auto_fill_location_from_lot
        )
        ICPSudo.set_param(
            'ai_sales_enhancement.enable_truck_tracking',
            self.enable_truck_tracking
        )
        ICPSudo.set_param(
            'ai_sales_enhancement.mandatory_truck_number',
            self.mandatory_truck_number
        )
