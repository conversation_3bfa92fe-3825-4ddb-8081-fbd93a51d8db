# AI BT Sales Extension

## Overview

This module extends the Odoo sales module with enhanced search capabilities and additional features focused on customer location-based filtering and sales management.

## Key Features

### Customer Location Search
- **City-based Search**: Search sales orders and quotations by customer city
- **State-based Search**: Filter orders by customer state/province
- **Country-based Search**: Filter orders by customer country
- **Enhanced Filters**: Quick filters for orders with/without location data

### List View Enhancements
- **Customer City Column**: Optional column showing customer city in list views
- **Customer State Column**: Optional column showing customer state in list views
- **Customer Country Column**: Optional column showing customer country in list views
- **Flexible Display**: All location columns are optional and can be shown/hidden as needed

### Grouping Options
- **Group by City**: Organize sales orders by customer city
- **Group by State**: Organize sales orders by customer state
- **Group by Country**: Organize sales orders by customer country

### Sales Agent Features
- Sales agent commission tracking
- Commission calculation and management
- Sales agent assignment to orders

## Usage

### Searching by Customer City

1. **In Sales Orders List**:
   - Go to Sales > Orders > Orders
   - Use the search bar and type in the "Customer City" field
   - Apply filters like "Has Customer City" to show only orders with city data

2. **In Quotations List**:
   - Go to Sales > Orders > Quotations
   - Same search functionality available as in orders

3. **Advanced Filtering**:
   - Use the filter dropdown to access location-based filters
   - Combine multiple location filters for precise results

### Viewing Customer Location in List Views

1. **Enable Location Columns**:
   - In any sales order or quotation list view
   - Click the column options (⚙️) button
   - Enable "Customer City", "Customer State", or "Customer Country" columns

2. **Grouping by Location**:
   - Use the "Group By" option in the search panel
   - Select "Customer City", "Customer State", or "Customer Country"
   - Orders will be organized by the selected location field

## Technical Details

### New Fields Added

The module adds the following related fields to the `sale.order` model:

- `partner_city`: Related field for customer city (stored)
- `partner_state`: Related field for customer state (stored)
- `partner_country`: Related field for customer country (stored)

### Views Modified

- **Sale Order Search View**: Enhanced with location search fields and filters
- **Sale Order List View**: Added optional location columns
- **Sale Quotation List View**: Added optional location columns

### Files Structure

```
ai_bt_sales_extension/
├── models/
│   ├── sale_order_city_extension.py    # New location fields
│   └── ...                             # Other existing models
├── views/
│   ├── sale_order_city_search_view.xml # Location search functionality
│   └── ...                             # Other existing views
└── ...
```

## Installation

1. Place the module in your Odoo addons directory
2. Update the app list in Odoo
3. Install the "AI BT Sales Extension" module
4. The location search functionality will be immediately available

## Dependencies

- `sale` - Odoo Sales module
- `base` - Odoo Base module
- Other dependencies as listed in `__manifest__.py`

## Compatibility

- Odoo 17.0+
- Compatible with existing sales workflows
- Does not modify core Odoo functionality

## Support

For issues or feature requests, please contact the development team.

## Version History

- **v1.0**: Initial release with customer location search functionality
