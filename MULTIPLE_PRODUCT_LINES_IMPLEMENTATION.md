# Multiple Product Lines Implementation for Sales Orders

## Overview

This implementation allows the same product to be added multiple times to a sale order with different rates, quantities, or other parameters. This functionality was previously only available in purchase orders and has now been extended to sales orders.

## Problem Solved

Previously, the sales module had validation that prevented using the same product with the same location but different rates. This limitation has been removed by implementing a line sequencing system similar to the one used in purchase orders.

## Implementation Details

### 1. Model Changes

#### SaleOrderLine Model Extensions

**File: `ai_bt_sales_extension/models/models.py`**

- Added `line_sequence` field to distinguish between multiple lines of the same product
- Modified `create()` method to automatically assign unique sequence numbers
- Enhanced line creation logic to handle multiple products properly

**Key Changes:**
```python
line_sequence = fields.Integer(
    string='Sequence',
    default=10,
    help="Unique sequence number for each line of the same product"
)

@api.model_create_multi
def create(self, vals_list):
    # Handle multiple lines of the same product with different rates
    for vals in vals_list:
        if vals.get('order_id') and vals.get('product_id'):
            existing_lines = self.search([
                ('order_id', '=', vals['order_id']),
                ('product_id', '=', vals['product_id'])
            ])
            if existing_lines:
                max_sequence = max(existing_lines.mapped('line_sequence')) if existing_lines.mapped('line_sequence') else 0
                vals['line_sequence'] = max_sequence + 10
```

#### Enhanced Methods

**File: `ai_bt_sales_extension/models/sale_models.py`**

- Added `_prepare_procurement_values()` method to handle procurement with line sequence
- Added `_prepare_invoice_line()` method to include sequence information in invoices
- Enhanced stock move creation to handle multiple lines properly

### 2. View Changes

#### Sale Order Form View

**File: `ai_bt_sales_extension/views/sales_views.xml`**

- Added `line_sequence` field to the sale order line list view
- Added `line_sequence` field to the sale order line form view
- Made the field optional but visible to help users distinguish between lines

**Key Changes:**
```xml
<field name="line_sequence" string="Line #" optional="show"/>
```

### 3. How It Works

#### Line Sequence Assignment

1. When creating the first line with a product: `line_sequence = 10`
2. When creating additional lines with the same product: `line_sequence = max_existing_sequence + 10`
3. This ensures each line has a unique sequence number

#### Example Scenario

```
Product: Rice (1kg bags)
Line 1: Qty=10, Rate=100, Sequence=10
Line 2: Qty=5,  Rate=120, Sequence=20  (same product, different rate)
Line 3: Qty=8,  Rate=90,  Sequence=30  (same product, different rate)
```

#### Invoice Generation

When invoices are created, lines with sequence > 10 will have "(Line X)" appended to their description to help identify them.

### 4. Benefits

1. **Flexibility**: Users can now add the same product multiple times with different:
   - Rates/Prices
   - Quantities
   - Payment terms (cash/bank split)
   - Locations (if applicable)

2. **Traceability**: Each line maintains its own sequence for easy identification

3. **Consistency**: Behavior now matches the purchase order functionality

4. **Backward Compatibility**: Existing sale orders continue to work normally

### 5. Testing

A test script (`test_multiple_product_lines.py`) has been provided to verify the functionality:

```bash
# Run in Odoo shell
python odoo-bin shell -d your_database_name
exec(open('test_multiple_product_lines.py').read())
```

### 6. Usage Instructions

1. **Create a Sale Order**: Start with a normal sale order
2. **Add First Product Line**: Add a product with desired quantity and rate
3. **Add Additional Lines**: Add the same product again with different parameters
4. **View Line Sequences**: Check the "Line #" column to see sequence numbers
5. **Process Normally**: Confirm, deliver, and invoice as usual

### 7. Technical Notes

#### Database Changes

- New field: `line_sequence` (Integer) added to `sale.order.line`
- No data migration required (defaults to 10 for existing records)

#### Performance Impact

- Minimal impact: Only one additional database query per line creation
- Sequence calculation is efficient and only runs when needed

#### Compatibility

- Compatible with existing custom modules
- Works with standard Odoo sales workflow
- Maintains all existing functionality

### 8. Future Enhancements

Potential future improvements could include:

1. **Bulk Line Creation**: Wizard to create multiple lines at once
2. **Line Grouping**: Visual grouping of lines with the same product
3. **Advanced Sequencing**: Custom sequence patterns or numbering
4. **Reporting Enhancements**: Reports that handle multiple product lines

### 9. Troubleshooting

#### Common Issues

1. **Sequence Not Updating**: Ensure the module is properly installed and updated
2. **View Not Showing Field**: Clear browser cache and refresh
3. **Permission Issues**: Ensure user has proper access rights

#### Debug Mode

Enable developer mode to see additional technical information about line sequences.

## Conclusion

This implementation successfully removes the validation that prevented using the same product with different rates in sales orders, providing users with the flexibility they need while maintaining data integrity and traceability.
