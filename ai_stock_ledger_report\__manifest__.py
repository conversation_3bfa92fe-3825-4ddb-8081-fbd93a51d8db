# -*- coding: utf-8 -*-
{
    'name': 'AI Stock Ledger Report',
    'version': '********.0',
    'summary': 'Generate detailed stock ledger reports in Excel format',
    'description': '''
        This module provides a comprehensive stock ledger report that shows:
        - Product-wise stock movements with lot tracking
        - Date-wise transaction details
        - Voucher numbers and account information
        - Quantity received, issued, and balance
        - Rate and amount calculations
        - Excel export functionality
        - Simple interface: select warehouses and optionally filter by products
    ''',
    'category': 'Warehouse',
    'author': 'AI Assistant',
    'depends': [
        'stock',
        'stock_account',
        'purchase',
        'sale',
    ],
    'data': [
        'security/ir.model.access.csv',
        'wizard/stock_ledger_report_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
