from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    # Truck number field for delivery tracking
    truck_number = fields.Char(
        string='Truck Number',
        help="Truck number for delivery tracking. Use this to search delivery notes by truck."
    )
    
    # Add remarks field and fix truck number transfer
    remarks = fields.Text(
        string='Remarks',
        help="Additional remarks or notes for this delivery"
    )

    @api.model
    def create(self, vals):
        """Override create to transfer truck number from sale order"""
        result = super(StockPicking, self).create(vals)
        
        # Transfer truck number from sale order to delivery
        if result.sale_id and result.sale_id.truck_number and not result.truck_number:
            result.truck_number = result.sale_id.truck_number
        
        return result
    
    # Enhanced search functionality
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """Enhanced search to include truck number"""
        if args is None:
            args = []
        
        # If searching by truck number, add it to search
        if name:
            truck_domain = [('truck_number', operator, name)]
            truck_pickings = self.search(truck_domain + args, limit=limit)
            if truck_pickings:
                # Combine with normal search results
                normal_results = super(StockPicking, self).name_search(
                    name, args, operator, limit
                )
                truck_results = [(p.id, p.display_name) for p in truck_pickings]
                
                # Remove duplicates and combine
                seen_ids = set()
                combined_results = []
                
                for result in truck_results + normal_results:
                    if result[0] not in seen_ids:
                        seen_ids.add(result[0])
                        combined_results.append(result)
                
                return combined_results[:limit]
        
        return super(StockPicking, self).name_search(name, args, operator, limit)

    def action_quick_lot_creation(self):
        """Open wizard for quick lot creation in delivery notes"""
        self.ensure_one()
        
        if self.picking_type_code != 'outgoing':
            raise UserError(_('Quick lot creation is only available for delivery orders.'))
        
        if self.state not in ['draft', 'waiting', 'confirmed', 'assigned']:
            raise UserError(_('Quick lot creation is only available for orders that are not yet done.'))
        
        # Get move lines that need lot assignment
        move_lines_needing_lots = self.move_line_ids.filtered(
            lambda ml: ml.product_id.tracking in ['lot', 'serial'] and not ml.lot_id
        )
        
        if not move_lines_needing_lots:
            raise UserError(_('No products requiring lot assignment found in this delivery.'))
        
        return {
            'name': _('Quick Lot Creation'),
            'type': 'ir.actions.act_window',
            'res_model': 'quick.lot.creation.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_picking_id': self.id,
                'default_move_line_ids': [(6, 0, move_lines_needing_lots.ids)],
            }
        }

    @api.model
    def search_by_truck_number(self, truck_number):
        """Search delivery notes by truck number"""
        if not truck_number:
            return self.browse([])
        
        return self.search([
            ('truck_number', 'ilike', truck_number),
            ('picking_type_code', '=', 'outgoing')
        ])

    def _get_truck_delivery_summary(self):
        """Get summary of deliveries for this truck"""
        self.ensure_one()
        
        if not self.truck_number:
            return {}
        
        # Find all deliveries with same truck number
        truck_deliveries = self.search([
            ('truck_number', '=', self.truck_number),
            ('picking_type_code', '=', 'outgoing')
        ])
        
        total_weight = 0.0
        total_orders = len(truck_deliveries)
        customers = set()
        
        for delivery in truck_deliveries:
            if delivery.partner_id:
                customers.add(delivery.partner_id.name)
            
            # Calculate total weight from move lines
            for move_line in delivery.move_line_ids:
                if move_line.product_id.weight:
                    total_weight += move_line.product_id.weight * move_line.qty_done
        
        return {
            'total_orders': total_orders,
            'total_weight': total_weight,
            'customers': list(customers),
            'delivery_ids': truck_deliveries.ids,
        }

    def action_view_truck_deliveries(self):
        """View all deliveries for this truck"""
        self.ensure_one()
        
        if not self.truck_number:
            raise UserError(_('No truck number specified for this delivery.'))
        
        truck_deliveries = self.search([
            ('truck_number', '=', self.truck_number),
            ('picking_type_code', '=', 'outgoing')
        ])
        
        return {
            'name': _('Deliveries for Truck %s') % self.truck_number,
            'type': 'ir.actions.act_window',
            'res_model': 'stock.picking',
            'view_mode': 'list,form',
            'domain': [('id', 'in', truck_deliveries.ids)],
            'context': {'search_default_truck_number': self.truck_number}
        }


class StockMove(models.Model):
    _inherit = 'stock.move'

    def action_create_lot_for_negative_stock(self):
        """Create lot for negative stock scenarios"""
        self.ensure_one()
        
        if self.product_id.tracking not in ['lot', 'serial']:
            raise UserError(_('This product does not require lot tracking.'))
        
        # Check if we're in a delivery context
        if self.picking_id and self.picking_id.picking_type_code == 'outgoing':
            return {
                'name': _('Create Lot for %s') % self.product_id.name,
                'type': 'ir.actions.act_window',
                'res_model': 'quick.lot.creation.wizard',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_picking_id': self.picking_id.id,
                    'default_product_id': self.product_id.id,
                    'default_move_id': self.id,
                    'default_quantity': self.product_uom_qty,
                }
            }
        
        raise UserError(_('Lot creation is only available for delivery orders.'))


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    def action_assign_lot_with_negative_stock(self):
        """Assign lot even with negative stock"""
        self.ensure_one()
        
        if self.product_id.tracking not in ['lot', 'serial']:
            raise UserError(_('This product does not require lot tracking.'))
        
        return {
            'name': _('Assign/Create Lot'),
            'type': 'ir.actions.act_window',
            'res_model': 'quick.lot.creation.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_picking_id': self.picking_id.id,
                'default_product_id': self.product_id.id,
                'default_move_line_id': self.id,
                'default_quantity': self.qty_done or self.product_uom_qty,
            }
        }

