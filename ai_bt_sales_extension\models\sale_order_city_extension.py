from odoo import models, fields, api


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Simple related field to access customer city
    partner_city = fields.Char(
        string='Customer City',
        related='partner_id.city',
        readonly=True,
        store=False,  # Explicitly set to False for Odoo 18 compatibility
        help="City of the customer from partner record"
    )
