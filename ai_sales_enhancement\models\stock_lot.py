from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class StockLot(models.Model):
    _inherit = 'stock.lot'

    # Enhanced weight display for lot selection
    display_weight_info = fields.Char(
        string='Weight Info',
        compute='_compute_display_weight_info',
        help="Display weight information for lot selection screens"
    )
    
    # Available quantity across all locations
    total_available_qty = fields.Float(
        string='Total Available',
        compute='_compute_total_available_qty',
        help="Total available quantity across all locations"
    )
    
    # Location breakdown
    location_breakdown = fields.Text(
        string='Location Breakdown',
        compute='_compute_location_breakdown',
        help="Breakdown of quantities by location"
    )

    @api.depends('weight', 'product_qty')
    def _compute_display_weight_info(self):
        """Compute display weight information"""
        for lot in self:
            if lot.weight and lot.product_qty:
                lot.display_weight_info = f"Qty: {lot.product_qty:.2f} | Weight: {lot.weight:.2f} kg"
            elif lot.product_qty:
                lot.display_weight_info = f"Qty: {lot.product_qty:.2f}"
            else:
                lot.display_weight_info = "No stock"

    @api.depends('quant_ids.quantity')
    def _compute_total_available_qty(self):
        """Compute total available quantity across all locations"""
        for lot in self:
            lot.total_available_qty = sum(lot.quant_ids.mapped('quantity'))

    @api.depends('quant_ids.quantity', 'quant_ids.location_id')
    def _compute_location_breakdown(self):
        """Compute location breakdown for better visibility"""
        for lot in self:
            breakdown_lines = []
            for quant in lot.quant_ids.filtered(lambda q: q.quantity > 0):
                weight_info = ""
                if lot.product_id.weight:
                    total_weight = quant.quantity * lot.product_id.weight
                    weight_info = f" ({total_weight:.2f} kg)"
                
                breakdown_lines.append(
                    f"{quant.location_id.name}: {quant.quantity:.2f}{weight_info}"
                )
            
            lot.location_breakdown = "\n".join(breakdown_lines) if breakdown_lines else "No stock"

    def name_get(self):
        """Enhanced name display with weight information"""
        result = []
        for lot in self:
            name = lot.name
            if lot.weight:
                name += f" (Weight: {lot.weight:.2f} kg)"
            if lot.total_available_qty:
                name += f" [Qty: {lot.total_available_qty:.2f}]"
            result.append((lot.id, name))
        return result

    @api.model
    def search_lots_with_stock(self, product_id=None, location_id=None):
        """Search lots with available stock"""
        domain = [('quant_ids.quantity', '>', 0)]
        
        if product_id:
            domain.append(('product_id', '=', product_id))
        
        if location_id:
            domain.append(('quant_ids.location_id', '=', location_id))
        
        return self.search(domain)

    def get_available_locations(self):
        """Get locations where this lot has stock"""
        self.ensure_one()
        return self.quant_ids.filtered(lambda q: q.quantity > 0).mapped('location_id')

    def get_stock_info_for_location(self, location_id):
        """Get stock information for specific location"""
        self.ensure_one()
        quant = self.quant_ids.filtered(lambda q: q.location_id.id == location_id)
        if quant:
            weight = 0.0
            if self.product_id.weight:
                weight = quant.quantity * self.product_id.weight
            
            return {
                'quantity': quant.quantity,
                'weight': weight,
                'location_name': quant.location_id.name,
            }
        return {
            'quantity': 0.0,
            'weight': 0.0,
            'location_name': '',
        }


class StockQuant(models.Model):
    _inherit = 'stock.quant'

    # Enhanced display for lot selection
    lot_weight_display = fields.Char(
        string='Lot Weight Display',
        compute='_compute_lot_weight_display',
        help="Display lot weight information in selection screens"
    )

    @api.depends('lot_id.weight', 'quantity', 'product_id.weight')
    def _compute_lot_weight_display(self):
        """Compute lot weight display for selection screens"""
        for quant in self:
            if quant.lot_id and quant.product_id.weight:
                total_weight = quant.quantity * quant.product_id.weight
                quant.lot_weight_display = f"Qty: {quant.quantity:.2f} | Weight: {total_weight:.2f} kg"
            elif quant.quantity:
                quant.lot_weight_display = f"Qty: {quant.quantity:.2f}"
            else:
                quant.lot_weight_display = "No stock"
