# Debug Guide: "Cannot convert undefined or null to object" Error

## Error Description
When clicking "Export to Excel", you get a JavaScript error: "cannot convert undefined or null to object"

## Root Causes & Solutions

### 1. **JavaScript Data Processing Error**
**Cause**: The action data structure is not properly formatted
**Solution**: Added comprehensive validation and error handling

### 2. **Missing Required Fields**
**Cause**: Required fields (warehouses, dates) are not selected
**Solution**: Added validation in Python export method

### 3. **JSON Serialization Issues**
**Cause**: Data cannot be properly serialized to JSON
**Solution**: Added proper JSON handling with error catching

## Debug Steps

### Step 1: Use Debug Buttons
1. Open the Stock Ledger Report wizard
2. Fill in the required fields (dates and warehouses)
3. Click "Test Export Data" button
4. Check the notification for data structure

### Step 2: Check Browser Console
1. Open browser developer tools (F12)
2. Go to Console tab
3. Try to export the report
4. Look for JavaScript errors and log messages

### Step 3: Check Odoo Logs
1. Check Odoo server logs for Python errors
2. Look for messages starting with "Stock Ledger Report Action:"
3. Check for controller errors

## Common Issues & Fixes

### Issue 1: No Warehouses Selected
**Error**: "Please select at least one warehouse"
**Fix**: Select at least one warehouse in the form

### Issue 2: Invalid Date Range
**Error**: "From date cannot be later than to date"
**Fix**: Ensure from date is before or equal to to date

### Issue 3: JavaScript Action Data Missing
**Error**: "Action data is missing"
**Fix**: This indicates a problem with the action return structure

### Issue 4: JSON Parsing Error
**Error**: "Invalid JSON in options"
**Fix**: Check data serialization in Python

## Enhanced Error Handling

The module now includes:

1. **Python Validation**:
   - Required field validation
   - Date range validation
   - Data structure validation

2. **JavaScript Validation**:
   - Action data validation
   - Proper error logging
   - Graceful error handling

3. **Controller Validation**:
   - Parameter validation
   - JSON parsing validation
   - Detailed error logging

## Testing the Fix

### Test 1: Basic Export
1. Select date range (e.g., last month)
2. Select at least one warehouse
3. Leave products empty
4. Click "Export to Excel"

### Test 2: With Product Filter
1. Select date range
2. Select warehouse(s)
3. Select some products
4. Click "Export to Excel"

### Test 3: Debug Information
1. Use "Test Export Data" button to verify data structure
2. Use "Debug Products" button to verify product availability
3. Check browser console for detailed logs

## If Error Persists

1. **Check Module Installation**:
   - Ensure module is properly installed
   - Update module if needed
   - Restart Odoo server

2. **Check Dependencies**:
   - Verify xlsxwriter is installed: `pip install xlsxwriter`
   - Check all required modules are installed

3. **Check Data**:
   - Ensure you have stock moves in the selected date range
   - Verify warehouses have stock moves
   - Check if products exist and are active

4. **Browser Issues**:
   - Clear browser cache
   - Try in incognito/private mode
   - Try different browser

## Log Analysis

Look for these log entries:

### Successful Export:
```
INFO: Stock Ledger Report Action: {action data}
INFO: Download data: {download data}
INFO: Generating report - Model: stock.ledger.report
INFO: Report generated successfully
```

### Error Patterns:
```
ERROR: Action data is missing
ERROR: Missing required action options
ERROR: Error generating stock ledger report
```

## Contact Support

If the error persists after trying all solutions:
1. Provide browser console logs
2. Provide Odoo server logs
3. Specify exact steps to reproduce
4. Include system information (Odoo version, browser, OS)
