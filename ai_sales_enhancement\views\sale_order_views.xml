<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enhanced Sale Order Form View -->
        <record id="view_order_form_enhanced" model="ir.ui.view">
            <field name="name">sale.order.form.enhanced</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="truck_number" placeholder="Enter truck number"/>
                    <field name="remarks"
                           placeholder="Enter any remarks or notes for this order"
                           help="Additional remarks that will be transferred to delivery orders"/>
                </xpath>

                <!-- Add Order-Level Quick Add functionality -->
                <xpath expr="//field[@name='order_line']" position="before">
                    <group string="Quick Add Products" col="3" invisible="state not in ('draft', 'sent')">
                        <field name="order_product_search"
                               placeholder="Enter product name or lot number to quickly add to order"
                               help="Search by product name or lot number to quickly add products to this order"/>
                        <button name="action_quick_add_product"
                                string="Add"
                                type="object"
                                class="btn-primary"
                                invisible="not order_product_search"
                                help="Add the searched product or lot to order lines"/>
                    </group>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Sale Order List View -->
        <record id="view_order_tree_enhanced" model="ir.ui.view">
            <field name="name">sale.order.tree.enhanced</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_tree"/>
            <field name="arch" type="xml">
                <!-- Add truck number after partner_id for better visibility -->
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="truck_number" string="Truck #" optional="show"/>
                </xpath>
                <!-- Add remarks after amount_total -->
                <xpath expr="//field[@name='amount_total']" position="after">
                    <field name="remarks" string="Remarks" optional="hide"/>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Sale Order Search View -->
        <record id="view_order_search_enhanced" model="ir.ui.view">
            <field name="name">sale.order.search.enhanced</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_sales_order_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="truck_number" string="Truck Number"/>
                    <field name="remarks" string="Remarks"/>
                </xpath>
                <xpath expr="//group" position="inside">
                    <filter string="Has Truck Number"
                            name="has_truck_number"
                            domain="[('truck_number', '!=', False)]"/>
                    <filter string="Has Remarks"
                            name="has_remarks"
                            domain="[('remarks', '!=', False)]"/>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Sale Order Line View with Additional Information -->
        <record id="view_order_form_line_enhanced" model="ir.ui.view">
            <field name="name">sale.order.form.line.enhanced</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="barcode_scanning_sale_purchase.view_order_form"/>
            <field name="arch" type="xml">
                <!-- Enhance the barcode_scan field with better placeholder and help -->
                <xpath expr="//field[@name='order_line']/list/field[@name='barcode_scan']" position="attributes">
                    <attribute name="placeholder">Enter lot number or scan barcode</attribute>
                    <attribute name="help">Enter the lot number or scan product barcode. The system will find the product and preserve the lot number value.</attribute>
                </xpath>

                <!-- Add additional information fields after product_id -->
                <xpath expr="//field[@name='order_line']/list/field[@name='product_id']" position="after">
                    <field name="available_qty_at_location" string="Available Qty" optional="show" readonly="1"/>
                    <field name="lot_weight" string="Lot Weight" optional="show" readonly="1"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>

