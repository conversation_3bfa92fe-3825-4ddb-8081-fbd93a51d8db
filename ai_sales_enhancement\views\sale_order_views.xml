<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enhanced Sale Order Form View -->
        <record id="view_order_form_enhanced" model="ir.ui.view">
            <field name="name">sale.order.form.enhanced</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="truck_number" placeholder="Enter truck number"/>
                    <field name="remarks"
                           placeholder="Enter any remarks or notes for this order"
                           help="Additional remarks that will be transferred to delivery orders"/>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Sale Order List View -->
        <record id="view_order_tree_enhanced" model="ir.ui.view">
            <field name="name">sale.order.tree.enhanced</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='activity_ids']" position="before">
                    <field name="truck_number" optional="show"/>
                    <field name="remarks" optional="show"/>
                </xpath>
            </field>
        </record>

        <!-- Enhanced Sale Order Search View -->
        <record id="view_order_search_enhanced" model="ir.ui.view">
            <field name="name">sale.order.search.enhanced</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_sales_order_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="truck_number" string="Truck Number"/>
                    <field name="remarks" string="Remarks"/>
                </xpath>
                <xpath expr="//group" position="inside">
                    <filter string="Has Truck Number"
                            name="has_truck_number"
                            domain="[('truck_number', '!=', False)]"/>
                    <filter string="Has Remarks"
                            name="has_remarks"
                            domain="[('remarks', '!=', False)]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>

