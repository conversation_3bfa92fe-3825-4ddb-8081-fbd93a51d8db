#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module validation script for AI Stock Ledger Report
"""

import os
import json

def validate_module():
    """Validate module structure and files"""
    print("🔍 Validating AI Stock Ledger Report module...")
    
    # Check required files
    required_files = [
        '__init__.py',
        '__manifest__.py',
        'controllers/__init__.py',
        'controllers/stock_ledger_controller.py',
        'wizard/__init__.py',
        'wizard/stock_ledger_report.py',
        'wizard/stock_ledger_report_views.xml',
        'security/ir.model.access.csv',
        'static/src/js/action_manager.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False
    
    # Check manifest file
    try:
        with open('__manifest__.py', 'r') as f:
            manifest_content = f.read()
            if "'name': 'AI Stock Ledger Report'" in manifest_content:
                print("✅ Manifest file contains correct module name")
            else:
                print("❌ Manifest file missing correct module name")
                return False
    except Exception as e:
        print(f"❌ Error reading manifest: {e}")
        return False
    
    # Check if xlsxwriter import works
    try:
        import xlsxwriter
        print("✅ xlsxwriter dependency available")
    except ImportError:
        print("⚠️  xlsxwriter not installed - run: pip install xlsxwriter")
    
    print("\n🎉 Module validation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Install xlsxwriter if not already installed: pip install xlsxwriter")
    print("2. Copy module to Odoo addons directory")
    print("3. Update apps list in Odoo")
    print("4. Install 'AI Stock Ledger Report' module")
    print("5. Access via Inventory > Reporting > Stock Ledger Report")
    
    return True

if __name__ == "__main__":
    # Change to module directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    validate_module()
