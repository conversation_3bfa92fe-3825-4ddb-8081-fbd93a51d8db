from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Truck number field for client orders
    truck_number = fields.Char(
        string='Truck Number',
        help="Truck number for this order. Clients can specify their preferred truck for delivery."
    )

    # Add remarks field to sale order
    remarks = fields.Text(
        string='Remarks',
        help="Additional remarks or notes for this order"
    )

    # Enhanced product search at order level
    order_product_search = fields.Char(
        string='Add Product/Lot',
        help="Search by product name or lot number to quickly add products to this order"
    )

    @api.onchange('order_product_search')
    def _onchange_order_product_search(self):
        """Enhanced search functionality at order level to add products to order lines"""
        # Don't process if we're in a problematic state
        if not self.order_product_search or not self._context.get('active_test', True):
            return

        search_term = self.order_product_search.strip()
        if not search_term:
            return

        # Avoid processing during onchange if order is not in draft state
        if hasattr(self, 'state') and self.state and self.state not in ('draft', 'sent'):
            return

        _logger.info("Order product search triggered with term: %s", search_term)

        # Use a separate transaction to avoid interfering with the main onchange
        try:
            # Don't modify the record during onchange, just show notification
            # The actual addition will be handled by the button action
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Search Ready'),
                    'message': _('Click the "Add" button to add "%s" to order lines.') % search_term,
                    'type': 'info',
                }
            }

        except Exception as e:
            _logger.error("Error in order product search onchange: %s", str(e))
            return

    def _add_product_from_lot(self, lot):
        """Add product to order line from selected lot"""
        self.ensure_one()

        try:
            # Ensure we have a valid order ID
            if not self.id:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _('Please save the order first before adding products.'),
                        'type': 'warning',
                    }
                }

            # Create new order line with the product from the lot
            line_vals = {
                'order_id': self.id,
                'product_id': lot.product_id.id,
                'product_uom_qty': 1.0,  # Default quantity
                'barcode_scan': lot.name,  # Use barcode_scan field to store lot name
            }

            # Find best location for this lot and add it
            try:
                quants = self.env['stock.quant'].search([
                    ('lot_id', '=', lot.id),
                    ('quantity', '>', 0)
                ], order='quantity desc', limit=1)

                if quants:
                    line_vals['line_location_id'] = quants[0].location_id.id
                    _logger.info("Auto-selected location %s for lot %s", quants[0].location_id.name, lot.name)
            except Exception as e:
                _logger.error("Error finding location for lot %s: %s", lot.name, str(e))

            # Create the order line
            new_line = self.env['sale.order.line'].create(line_vals)

            # Trigger onchange to set proper values (with error handling)
            try:
                new_line._onchange_product_id()
            except Exception:
                # If onchange fails, the line is still created
                pass

            # Log the successful creation
            _logger.info("Successfully created order line with product: %s from lot: %s (ID: %s)",
                        lot.product_id.name, lot.name, new_line.id)

            # Clear the search field
            self.order_product_search = ''

            # Return a reload action to refresh the view
            return {
                'type': 'ir.actions.client',
                'tag': 'reload',
            }
        except Exception as e:
            _logger.error("Error adding product from lot: %s", str(e))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to add product. Please try again.'),
                    'type': 'danger',
                }
            }

    def _add_product_to_order_line(self, product):
        """Add product to order line with lot selection if needed"""
        self.ensure_one()

        try:
            # Ensure we have a valid order ID
            if not self.id:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _('Please save the order first before adding products.'),
                        'type': 'warning',
                    }
                }

            # Check if product requires lot tracking and has multiple lots
            try:
                if hasattr(product, 'tracking') and product.tracking in ['lot', 'serial']:
                    # Simplified lot search to avoid complex queries
                    available_lots = self.env['stock.lot'].search([
                        ('product_id', '=', product.id)
                    ], limit=10)

                    # Filter lots that have stock
                    lots_with_stock = []
                    for lot in available_lots:
                        try:
                            quants = self.env['stock.quant'].search([
                                ('lot_id', '=', lot.id),
                                ('quantity', '>', 0)
                            ], limit=1)
                            if quants:
                                lots_with_stock.append(lot)
                        except Exception:
                            continue

                    if len(lots_with_stock) > 1:
                        # Multiple lots available, show selection wizard
                        return self._show_lot_selection_wizard(product, lots_with_stock)
                    elif len(lots_with_stock) == 1:
                        # Only one lot available, use it
                        lot_name = lots_with_stock[0].name
                    else:
                        # No lots with stock, create line without lot
                        lot_name = False
                else:
                    lot_name = False
            except Exception as e:
                _logger.error("Error checking product tracking: %s", str(e))
                lot_name = False

            line_vals = {
                'order_id': self.id,
                'product_id': product.id,
                'product_uom_qty': 1.0,  # Default quantity
            }

            if lot_name:
                line_vals['barcode_scan'] = lot_name

            # Create the order line
            new_line = self.env['sale.order.line'].create(line_vals)

            # Trigger onchange to set proper values (with error handling)
            try:
                new_line._onchange_product_id()
            except Exception as e:
                _logger.error("Error in _onchange_product_id: %s", str(e))
                # If onchange fails, the line is still created
                pass

            # Log the successful creation
            _logger.info("Successfully created order line with product: %s (ID: %s)", product.name, new_line.id)

            # Clear the search field
            self.order_product_search = ''

            # Return a reload action to refresh the view
            return {
                'type': 'ir.actions.client',
                'tag': 'reload',
            }
        except Exception as e:
            _logger.error("Error adding product to order line: %s", str(e))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to add product. Please try again.'),
                    'type': 'danger',
                }
            }

    def action_quick_add_product(self):
        """Manual action to add product when button is clicked"""
        self.ensure_one()

        _logger.info("Quick add product action triggered with search term: %s", self.order_product_search)

        if not self.order_product_search:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Search Term'),
                    'message': _('Please enter a product name or lot number to search.'),
                    'type': 'warning',
                }
            }

        search_term = self.order_product_search.strip()
        if not search_term:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Invalid Search'),
                    'message': _('Please enter a valid product name or lot number.'),
                    'type': 'warning',
                }
            }

        try:
            # First, try to find by lot number (exact match first)
            try:
                lots = self.env['stock.lot'].search([
                    ('name', '=', search_term)
                ], limit=1)

                if lots:
                    _logger.info("Found exact lot match: %s", lots[0].name)
                    result = self._add_product_from_lot(lots[0])
                    self.order_product_search = ''  # Clear search after adding
                    return result
            except Exception as e:
                _logger.error("Error searching lots: %s", str(e))

            # If no exact lot match, search by product name (exact match first)
            try:
                products = self.env['product.product'].search([
                    '|',
                    ('name', '=', search_term),
                    ('default_code', '=', search_term),
                    ('sale_ok', '=', True)
                ], limit=1)

                if products:
                    _logger.info("Found exact product match: %s", products[0].name)
                    result = self._add_product_to_order_line(products[0])
                    self.order_product_search = ''  # Clear search after adding
                    return result
            except Exception as e:
                _logger.error("Error searching products: %s", str(e))

            # No exact matches found, try fuzzy search
            try:
                fuzzy_products = self.env['product.product'].search([
                    '|',
                    ('name', 'ilike', search_term),
                    ('default_code', 'ilike', search_term),
                    ('sale_ok', '=', True)
                ], limit=5)

                if fuzzy_products:
                    if len(fuzzy_products) == 1:
                        result = self._add_product_to_order_line(fuzzy_products[0])
                        self.order_product_search = ''  # Clear search after adding
                        return result
                    else:
                        # Multiple matches found, show them to user
                        try:
                            product_names = ', '.join(fuzzy_products.mapped('name')[:3])
                            if len(fuzzy_products) > 3:
                                product_names += f' and {len(fuzzy_products) - 3} more...'

                            return {
                                'type': 'ir.actions.client',
                                'tag': 'display_notification',
                                'params': {
                                    'title': _('Multiple Matches'),
                                    'message': _('Found multiple products: %s. Please be more specific.') % product_names,
                                    'type': 'info',
                                }
                            }
                        except Exception as e:
                            _logger.error("Error formatting product names: %s", str(e))
                            return {
                                'type': 'ir.actions.client',
                                'tag': 'display_notification',
                                'params': {
                                    'title': _('Multiple Matches'),
                                    'message': _('Found %d products. Please be more specific.') % len(fuzzy_products),
                                    'type': 'info',
                                }
                            }
            except Exception as e:
                _logger.error("Error in fuzzy search: %s", str(e))

            # No matches found
            _logger.info("No matches found for search term: %s", search_term)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Results'),
                    'message': _('No products or lots found for "%s". Please check the spelling.') % search_term,
                    'type': 'warning',
                }
            }

        except Exception as e:
            _logger.error("Error in quick add product: %s", str(e))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('An error occurred while searching. Please try again.'),
                    'type': 'danger',
                }
            }

    def _show_lot_selection_wizard(self, product, lots):
        """Show wizard to select lot when product has multiple lots"""
        _logger.info("Opening lot selection wizard for product %s with %d lots", product.name, len(lots))

        # Convert lots to list if it's a recordset
        if hasattr(lots, 'ids'):
            lot_ids = lots.ids
        else:
            lot_ids = [lot.id for lot in lots]

        return {
            'name': _('Select Lot for %s') % product.name,
            'type': 'ir.actions.act_window',
            'res_model': 'order.product.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_id': self.id,
                'default_search_term': product.name,
                'default_selected_product_id': product.id,
                'default_lot_ids': [(6, 0, lot_ids)],
                'search_type': 'lot_selection'
            }
        }

    def _show_lot_selection_for_order(self, lots):
        """Show lot selection wizard for order level search"""
        return {
            'name': _('Select Lot'),
            'type': 'ir.actions.act_window',
            'res_model': 'order.product.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_id': self.id,
                'default_search_term': self.order_product_search,
                'default_lot_ids': [(6, 0, lots.ids)],
                'search_type': 'lot'
            }
        }

    def _show_product_selection_for_order(self, products):
        """Show product selection wizard for order level search"""
        return {
            'name': _('Select Product'),
            'type': 'ir.actions.act_window',
            'res_model': 'order.product.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_id': self.id,
                'default_search_term': self.order_product_search,
                'default_product_ids': [(6, 0, products.ids)],
                'search_type': 'product'
            }
        }

    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """Enhanced search to include truck number"""
        if args is None:
            args = []

        # If searching and truck number field exists, include it in search
        if name and hasattr(self, 'truck_number'):
            truck_domain = [('truck_number', operator, name)]
            truck_orders = self.search(truck_domain + args, limit=limit)

            if truck_orders:
                # Combine with standard name search
                standard_results = super(SaleOrder, self).name_search(name, args, operator, limit)

                # Create combined results, avoiding duplicates
                truck_results = [(order.id, f"{order.name} (Truck: {order.truck_number})")
                               for order in truck_orders if order.truck_number]

                # Merge results, removing duplicates
                all_ids = set()
                combined_results = []

                for result in truck_results + standard_results:
                    if result[0] not in all_ids:
                        all_ids.add(result[0])
                        combined_results.append(result)

                return combined_results[:limit]

        return super(SaleOrder, self).name_search(name, args, operator, limit)

    def action_open_lot_location_wizard(self):
        """Open wizard for lot number and location selection after confirmation"""
        self.ensure_one()
        
        if self.state not in ['sale', 'done']:
            raise UserError(_('This action is only available for confirmed sales orders.'))
        
        # Get all sale order lines that need lot/location assignment
        lines_needing_assignment = self.order_line.filtered(
            lambda l: l.product_id.tracking in ['lot', 'serial'] and l.product_uom_qty > 0
        )
        
        if not lines_needing_assignment:
            raise UserError(_('No products requiring lot number assignment found in this order.'))
        
        return {
            'name': _('Select Lot Numbers and Locations'),
            'type': 'ir.actions.act_window',
            'res_model': 'lot.location.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_id': self.id,
                'default_sale_order_line_ids': [(6, 0, lines_needing_assignment.ids)],
            }
        }

    def _prepare_picking_vals(self, picking_type):
        """Override to include truck number and remarks in delivery creation"""
        vals = super(SaleOrder, self)._prepare_picking_vals(picking_type)
        if self.truck_number:
            vals['truck_number'] = self.truck_number
        if self.remarks:
            vals['remarks'] = self.remarks
        return vals


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Display fields for better UX (using barcode_scan field for lot tracking)
    available_qty_at_location = fields.Float(
        string='Available Qty',
        compute='_compute_available_qty_at_location',
        help="Available quantity at selected location"
    )

    lot_weight = fields.Float(
        string='Lot Weight',
        compute='_compute_lot_weight',
        help="Total weight of the selected lot"
    )

    @api.depends('barcode_scan', 'product_id', 'line_location_id')
    def _compute_available_qty_at_location(self):
        """Compute available quantity for selected lot using barcode_scan field"""
        for line in self:
            line.available_qty_at_location = 0.0
            try:
                if line.barcode_scan and line.product_id:
                    # Find lot by barcode_scan value
                    lot = self.env['stock.lot'].search([
                        ('name', '=', line.barcode_scan),
                        ('product_id', '=', line.product_id.id)
                    ], limit=1)

                    if lot:
                        # Build search domain for quants
                        quant_domain = [
                            ('product_id', '=', line.product_id.id),
                            ('lot_id', '=', lot.id),
                            ('quantity', '>', 0),
                        ]

                        # If specific location is set, filter by that location
                        if line.line_location_id:
                            quant_domain.append(('location_id', '=', line.line_location_id.id))

                        quants = self.env['stock.quant'].search(quant_domain)
                        line.available_qty_at_location = sum(quants.mapped('quantity'))
            except Exception:
                # If there's any error, keep the default value of 0
                pass

    @api.depends('barcode_scan', 'product_id')
    def _compute_lot_weight(self):
        """Compute total weight of selected lot using barcode_scan field"""
        for line in self:
            line.lot_weight = 0.0
            try:
                if line.barcode_scan and line.product_id:
                    # Find lot by barcode_scan value
                    lot = self.env['stock.lot'].search([
                        ('name', '=', line.barcode_scan),
                        ('product_id', '=', line.product_id.id)
                    ], limit=1)

                    if lot and hasattr(lot, 'weight'):
                        line.lot_weight = lot.weight
            except Exception:
                # If there's any error, keep the default value of 0
                pass

    @api.onchange('barcode_scan')
    def _onchange_barcode_scan_enhanced(self):
        """Enhanced barcode scan functionality with auto lot selection"""
        # Call the parent method first to get the basic functionality
        result = super(SaleOrderLine, self)._onchange_barcode_scan()

        # The parent method already handles the barcode_scan logic properly
        # We just need to ensure our enhanced fields are computed
        return result



    def action_open_product_wizard(self):
        """Open wizard to select product when multiple products are found for the same lot"""
        self.ensure_one()

        if not self.barcode_scan:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lot Number',
                    'message': 'Please enter a lot number first.',
                    'type': 'warning',
                }
            }

        # Find products for this lot number
        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])
        if not lots:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lots Found',
                    'message': f'No lots found for lot number {self.barcode_scan}',
                    'type': 'warning',
                }
            }

        products = lots.mapped('product_id')
        if not products:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Products Found',
                    'message': f'No products found for lot number {self.barcode_scan}',
                    'type': 'warning',
                }
            }

        # Create wizard record
        wizard = self.env['product.selection.wizard'].create({
            'lot_number': self.barcode_scan,
            'source_model': self._name,
            'source_record_id': self.id,
        })

        # Create wizard lines for each product
        wizard_lines = []
        for product in products:
            wizard_lines.append((0, 0, {
                'product_id': product.id,
                'is_selected': False,
            }))

        wizard.line_ids = wizard_lines

        # Return action to open wizard
        return {
            'name': f'Select Product for Lot {self.barcode_scan}',
            'type': 'ir.actions.act_window',
            'res_model': 'product.selection.wizard',
            'res_id': wizard.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_open_lot_selection_wizard(self):
        """Open wizard to select from available lots"""
        self.ensure_one()
        
        if not self.product_id:
            raise UserError(_('Please select a product first.'))
        
        return {
            'name': _('Select Lot and Location'),
            'type': 'ir.actions.act_window',
            'res_model': 'lot.location.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_line_ids': [(6, 0, [self.id])],
                'default_product_id': self.product_id.id,
            }
        }

