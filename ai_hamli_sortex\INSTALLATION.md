# Installation and Usage Guide

## Prerequisites

Before installing the `ai_hamli_sortex` module, ensure you have:

1. **Odoo 18.0** or compatible version
2. **ai_bt_spices_module** installed and configured
3. **Manufacturing (MRP)** module enabled
4. Appropriate user permissions

## Installation Steps

### 1. Module Installation

1. **Copy the module** to your Odoo addons directory:
   ```
   cp -r ai_hamli_sortex /path/to/your/odoo/addons/
   ```

2. **Update the apps list** in Odoo:
   - Go to Apps menu
   - Click "Update Apps List"
   - Search for "AI Hamli Sortex Cost Editor"

3. **Install the module**:
   - Click "Install" on the module card
   - Wait for installation to complete

### 2. User Permissions Setup

1. **Assign security groups** to users:
   - Go to Settings → Users & Companies → Users
   - Edit the user who needs access
   - In the "Access Rights" tab, assign one of:
     - **<PERSON>li Sortex Cost Editor**: Can edit costs
     - **Hamli Sortex Cost Manager**: Can edit costs and manage all overrides

### 3. Verification

1. **Check installation**:
   - Go to Manufacturing → Manufacturing Orders
   - Open any manufacturing order (any state)
   - Look for the "Hamli & Sortex Cost Override" tab

## Usage Instructions

### Basic Workflow

1. **Access Manufacturing Order**:
   - Open any manufacturing order (any state)
   - Navigate to "Hamli & Sortex Cost Override" tab

2. **Enable Cost Override**:
   - Click "Enable Override" for hamli or sortex costs
   - The manual cost field will be populated with current automatic value

3. **Edit Costs**:
   - Modify the manual cost values as needed
   - All dependent costs will automatically recalculate

4. **Manage Overrides**:
   - Use "Disable Override" to return to automatic calculation
   - Use "Recalculate Costs" to force recalculation

### Post-Production Cost Adjustment

This module is especially useful for adjusting costs after manufacturing is completed:

1. **After Manufacturing Completion**:
   - Manufacturing order is marked as "Done"
   - All automatic calculations are complete

2. **Cost Review and Adjustment**:
   - Review the automatically calculated hamli and sortex costs
   - Enable override mode for costs that need adjustment
   - Enter the correct cost values based on actual expenses

3. **Final Cost Calculation**:
   - All dependent costs (CI landed cost, total cost) automatically update
   - Final manufacturing cost reflects actual expenses

### Advanced Features

#### Audit Trail
- View who last modified costs and when
- Available to users with "Hamli Sortex Cost Manager" role

#### Production B Support
- Sortex cost overrides are automatically disabled for Production B orders
- Hamli cost overrides remain available

#### Flexible State Access
- Cost overrides can be enabled in any manufacturing order state
- Particularly useful for post-production cost adjustments

## Troubleshooting

### Common Issues

1. **"Enable Override" button not visible**:
   - Check user has "Hamli Sortex Cost Editor" permissions
   - Verify the module is properly installed

2. **Manual cost fields are readonly**:
   - Verify override mode is enabled
   - Check user permissions

3. **Costs not recalculating**:
   - Click "Recalculate Costs" button
   - Check for any validation errors in the logs

4. **Sortex override disabled**:
   - Check if the order is marked as "Production B"
   - Production B orders don't use sortex costs

### Error Messages

- **"Manual hamli/sortex cost cannot be negative"**:
  - Enter positive values only

### Support

For technical support:
1. Check the Odoo logs for detailed error messages
2. Verify all dependencies are properly installed
3. Ensure user permissions are correctly configured

## Integration Notes

### Compatibility
- Works seamlessly with existing `ai_bt_spices_module` functionality
- Preserves all original automatic calculations
- Backward compatible with existing manufacturing orders

### Data Migration
- No data migration required
- Existing manufacturing orders will continue to work normally
- Override functionality is available immediately after installation

### Performance
- Minimal performance impact
- Calculations only triggered when override values change
- Efficient dependency tracking for computed fields
