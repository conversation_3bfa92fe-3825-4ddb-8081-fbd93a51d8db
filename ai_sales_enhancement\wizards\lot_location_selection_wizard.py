from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class LotLocationSelectionWizard(models.TransientModel):
    _name = 'lot.location.selection.wizard'
    _description = 'Lot and Location Selection Wizard'

    sale_order_id = fields.Many2one(
        'sale.order',
        string='Sale Order',
        readonly=True
    )
    
    sale_order_line_ids = fields.Many2many(
        'sale.order.line',
        string='Sale Order Lines'
    )
    
    line_ids = fields.One2many(
        'lot.location.selection.wizard.line',
        'wizard_id',
        string='Lines'
    )

    @api.model
    def default_get(self, fields_list):
        """Set default values"""
        res = super().default_get(fields_list)
        
        sale_order_line_ids = self.env.context.get('default_sale_order_line_ids', [])
        if sale_order_line_ids and isinstance(sale_order_line_ids[0], tuple):
            sale_order_line_ids = sale_order_line_ids[0][2]
        
        if sale_order_line_ids:
            lines = []
            for line_id in sale_order_line_ids:
                line = self.env['sale.order.line'].browse(line_id)
                if line.product_id.tracking in ['lot', 'serial']:
                    lines.append((0, 0, {
                        'sale_order_line_id': line.id,
                        'product_id': line.product_id.id,
                        'quantity_needed': line.product_uom_qty,
                        'selected_lot_id': line.selected_lot_id.id if line.selected_lot_id else False,
                        'selected_location_id': line.selected_location_id.id if line.selected_location_id else False,
                    }))
            res['line_ids'] = lines
        
        return res

    def action_confirm(self):
        """Confirm lot and location selections"""
        for line in self.line_ids:
            if line.selected_lot_id and line.selected_location_id:
                # Update the sale order line
                line.sale_order_line_id.write({
                    'selected_lot_id': line.selected_lot_id.id,
                    'selected_location_id': line.selected_location_id.id,
                })
                
                # If sale order is confirmed, update stock moves
                if line.sale_order_line_id.order_id.state == 'sale':
                    self._update_stock_moves(line)
        
        return {'type': 'ir.actions.act_window_close'}

    def _update_stock_moves(self, wizard_line):
        """Update stock moves with selected lot and location"""
        sale_line = wizard_line.sale_order_line_id
        
        # Find related stock moves
        stock_moves = self.env['stock.move'].search([
            ('sale_line_id', '=', sale_line.id),
            ('state', 'not in', ['done', 'cancel'])
        ])
        
        for move in stock_moves:
            # Update move lines
            for move_line in move.move_line_ids:
                if not move_line.lot_id and move_line.product_id.id == wizard_line.product_id.id:
                    move_line.write({
                        'lot_id': wizard_line.selected_lot_id.id,
                        'location_id': wizard_line.selected_location_id.id,
                    })


class LotLocationSelectionWizardLine(models.TransientModel):
    _name = 'lot.location.selection.wizard.line'
    _description = 'Lot and Location Selection Wizard Line'

    wizard_id = fields.Many2one(
        'lot.location.selection.wizard',
        string='Wizard',
        required=True,
        ondelete='cascade'
    )
    
    sale_order_line_id = fields.Many2one(
        'sale.order.line',
        string='Sale Order Line',
        required=True
    )
    
    product_id = fields.Many2one(
        'product.product',
        string='Product',
        required=True
    )
    
    quantity_needed = fields.Float(
        string='Quantity Needed',
        required=True
    )
    
    selected_lot_id = fields.Many2one(
        'stock.lot',
        string='Selected Lot',
        domain="[('product_id', '=', product_id)]"
    )
    
    selected_location_id = fields.Many2one(
        'stock.location',
        string='Selected Location',
        domain="[('usage', '=', 'internal')]"
    )
    
    available_qty_at_location = fields.Float(
        string='Available Qty',
        compute='_compute_available_qty_at_location'
    )
    
    lot_weight = fields.Float(
        string='Lot Weight',
        compute='_compute_lot_weight'
    )

    # Additional weight fields
    product_weight = fields.Float(
        string='Unit Weight (kg)',
        related='product_id.weight',
        readonly=True,
        help="Weight per unit of the product"
    )

    total_weight = fields.Float(
        string='Total Weight (kg)',
        compute='_compute_total_weight',
        help="Total weight based on quantity and unit weight"
    )

    available_lots = fields.Text(
        string='Available Lots',
        compute='_compute_available_lots'
    )

    @api.depends('selected_lot_id', 'selected_location_id')
    def _compute_available_qty_at_location(self):
        """Compute available quantity at selected location"""
        for line in self:
            if line.selected_lot_id and line.selected_location_id:
                quants = self.env['stock.quant'].search([
                    ('product_id', '=', line.product_id.id),
                    ('lot_id', '=', line.selected_lot_id.id),
                    ('location_id', '=', line.selected_location_id.id),
                ])
                line.available_qty_at_location = sum(quants.mapped('quantity'))
            else:
                line.available_qty_at_location = 0.0

    @api.depends('selected_lot_id')
    def _compute_lot_weight(self):
        """Compute lot weight"""
        for line in self:
            if line.selected_lot_id:
                line.lot_weight = line.selected_lot_id.weight
            else:
                line.lot_weight = 0.0

    @api.depends('quantity', 'product_id.weight')
    def _compute_total_weight(self):
        """Compute total weight based on quantity and product weight"""
        for line in self:
            if line.product_id and line.product_id.weight:
                line.total_weight = line.quantity * line.product_id.weight
            else:
                line.total_weight = 0.0

    @api.depends('product_id')
    def _compute_available_lots(self):
        """Compute available lots for the product"""
        for line in self:
            if line.product_id:
                lots = self.env['stock.lot'].search([
                    ('product_id', '=', line.product_id.id),
                    ('quant_ids.quantity', '>', 0)
                ])
                
                lot_info = []
                for lot in lots:
                    total_qty = sum(lot.quant_ids.mapped('quantity'))
                    weight_info = f" ({lot.weight:.2f} kg)" if lot.weight else ""
                    lot_info.append(f"{lot.name}: {total_qty:.2f}{weight_info}")
                
                line.available_lots = "\n".join(lot_info) if lot_info else "No lots with stock"
            else:
                line.available_lots = ""

    @api.onchange('selected_lot_id')
    def _onchange_selected_lot_id(self):
        """Auto-select best location when lot is selected"""
        if self.selected_lot_id:
            # Find location with highest stock for this lot
            quants = self.env['stock.quant'].search([
                ('product_id', '=', self.product_id.id),
                ('lot_id', '=', self.selected_lot_id.id),
                ('quantity', '>', 0),
            ], order='quantity desc', limit=1)
            
            if quants:
                self.selected_location_id = quants[0].location_id

    @api.onchange('product_id')
    def _onchange_product_id(self):
        """Reset lot and location when product changes"""
        self.selected_lot_id = False
        self.selected_location_id = False
