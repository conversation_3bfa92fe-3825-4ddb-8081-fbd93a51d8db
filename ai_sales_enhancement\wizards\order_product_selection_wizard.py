from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class OrderProductSelectionWizard(models.TransientModel):
    _name = 'order.product.selection.wizard'
    _description = 'Product Selection Wizard for Sales Orders'

    sale_order_id = fields.Many2one(
        'sale.order',
        string='Sales Order',
        required=True
    )
    
    search_term = fields.Char(
        string='Search Term',
        readonly=True
    )
    
    search_type = fields.Selection([
        ('product', 'Product'),
        ('lot', 'Lot'),
        ('lot_selection', 'Lot Selection')
    ], string='Search Type', default='product')
    
    # For product search results
    product_ids = fields.Many2many(
        'product.product',
        string='Available Products'
    )
    
    selected_product_id = fields.Many2one(
        'product.product',
        string='Selected Product',
        domain="[('id', 'in', product_ids)]"
    )
    
    # For lot search results
    lot_ids = fields.Many2many(
        'stock.lot',
        string='Available Lots'
    )
    
    selected_lot_id = fields.Many2one(
        'stock.lot',
        string='Selected Lot',
        domain="[('id', 'in', lot_ids)]"
    )
    
    # Product details
    product_info = fields.Text(
        string='Product Information',
        compute='_compute_product_info'
    )
    
    # Quantity to add
    quantity = fields.Float(
        string='Quantity',
        default=1.0,
        required=True
    )
    
    # Location selection for lots
    available_locations = fields.Text(
        string='Available Locations',
        compute='_compute_available_locations'
    )
    
    selected_location_id = fields.Many2one(
        'stock.location',
        string='Location',
        domain="[('usage', '=', 'internal')]"
    )

    @api.model
    def default_get(self, fields_list):
        """Set default values from context"""
        res = super(OrderProductSelectionWizard, self).default_get(fields_list)

        context = self.env.context

        # Set search type from context
        if context.get('search_type'):
            res['search_type'] = context.get('search_type')

        # Set sale order
        if context.get('default_sale_order_id'):
            res['sale_order_id'] = context.get('default_sale_order_id')

        # Set search term
        if context.get('default_search_term'):
            res['search_term'] = context.get('default_search_term')

        # Set selected product
        if context.get('default_selected_product_id'):
            res['selected_product_id'] = context.get('default_selected_product_id')

        # Set lot IDs for lot selection
        if context.get('default_lot_ids'):
            res['lot_ids'] = context.get('default_lot_ids')
            _logger.info("Setting lot_ids from context: %s", context.get('default_lot_ids'))

        return res

    @api.depends('selected_product_id', 'selected_lot_id')
    def _compute_product_info(self):
        """Compute product information display"""
        for wizard in self:
            info_lines = []
            
            if wizard.search_type == 'product' and wizard.selected_product_id:
                product = wizard.selected_product_id
                info_lines.append(f"Product: {product.name}")
                if product.default_code:
                    info_lines.append(f"Code: {product.default_code}")
                if product.weight:
                    info_lines.append(f"Weight: {product.weight:.2f} kg")
                
                # Show available lots for this product
                lots = self.env['stock.lot'].search([
                    ('product_id', '=', product.id),
                    ('quant_ids.quantity', '>', 0)
                ])
                if lots:
                    info_lines.append(f"Available Lots: {len(lots)}")
                    for lot in lots[:5]:  # Show first 5 lots
                        total_qty = sum(lot.quant_ids.mapped('quantity'))
                        info_lines.append(f"  - {lot.name}: {total_qty:.2f}")
                    if len(lots) > 5:
                        info_lines.append(f"  ... and {len(lots) - 5} more")
            
            elif wizard.search_type in ['lot', 'lot_selection'] and wizard.selected_lot_id:
                lot = wizard.selected_lot_id
                info_lines.append(f"Lot: {lot.name}")
                info_lines.append(f"Product: {lot.product_id.name}")
                if hasattr(lot, 'weight') and lot.weight:
                    info_lines.append(f"Total Weight: {lot.weight:.2f} kg")

                try:
                    total_qty = sum(lot.quant_ids.mapped('quantity'))
                    info_lines.append(f"Available Quantity: {total_qty:.2f}")
                except Exception:
                    info_lines.append("Available Quantity: Unknown")

            elif wizard.search_type == 'lot_selection' and not wizard.selected_lot_id:
                # Show information about available lots for selection
                if wizard.lot_ids:
                    info_lines.append(f"Please select from {len(wizard.lot_ids)} available lots:")
                    for lot in wizard.lot_ids[:3]:  # Show first 3 lots
                        try:
                            total_qty = sum(lot.quant_ids.mapped('quantity'))
                            info_lines.append(f"  - {lot.name}: {total_qty:.2f} available")
                        except Exception:
                            info_lines.append(f"  - {lot.name}: Available")
                    if len(wizard.lot_ids) > 3:
                        info_lines.append(f"  ... and {len(wizard.lot_ids) - 3} more")

            wizard.product_info = "\n".join(info_lines) if info_lines else "No product selected"

    @api.depends('selected_lot_id')
    def _compute_available_locations(self):
        """Compute available locations for selected lot"""
        for wizard in self:
            if wizard.selected_lot_id:
                try:
                    # Search for quants with stock for this lot
                    quants = self.env['stock.quant'].search([
                        ('lot_id', '=', wizard.selected_lot_id.id),
                        ('quantity', '>', 0)
                    ])

                    location_info = []
                    for quant in quants:
                        location_info.append(f"{quant.location_id.name}: {quant.quantity:.2f}")

                    wizard.available_locations = "\n".join(location_info) if location_info else "No stock available"
                except Exception as e:
                    _logger.error("Error computing available locations: %s", str(e))
                    wizard.available_locations = "Error loading locations"
            else:
                wizard.available_locations = ""

    @api.onchange('selected_lot_id')
    def _onchange_selected_lot_id(self):
        """Auto-select best location when lot is selected"""
        if self.selected_lot_id:
            _logger.info("Lot selected: %s, finding best location", self.selected_lot_id.name)
            try:
                # Find location with highest stock for this lot
                quants = self.env['stock.quant'].search([
                    ('lot_id', '=', self.selected_lot_id.id),
                    ('quantity', '>', 0)
                ])

                _logger.info("Found %d quants for lot %s", len(quants), self.selected_lot_id.name)

                if quants:
                    best_quant = quants.sorted('quantity', reverse=True)[0]
                    self.selected_location_id = best_quant.location_id
                    _logger.info("Auto-selected location: %s with quantity: %s",
                               best_quant.location_id.name, best_quant.quantity)

                    # Update domain to show only locations with stock for this lot
                    location_ids = quants.mapped('location_id').ids
                    return {
                        'domain': {
                            'selected_location_id': [('id', 'in', location_ids)]
                        }
                    }
                else:
                    _logger.warning("No quants with stock found for lot %s", self.selected_lot_id.name)
                    self.selected_location_id = False
                    return {
                        'domain': {
                            'selected_location_id': [('id', '=', False)]
                        }
                    }
            except Exception as e:
                _logger.error("Error in lot selection onchange: %s", str(e))
                self.selected_location_id = False
                return {
                    'domain': {
                        'selected_location_id': [('usage', '=', 'internal')]
                    }
                }
        else:
            self.selected_location_id = False
            return {
                'domain': {
                    'selected_location_id': [('usage', '=', 'internal')]
                }
            }

    def action_refresh_locations(self):
        """Manually refresh locations for selected lot"""
        if self.selected_lot_id:
            _logger.info("Manually refreshing locations for lot: %s", self.selected_lot_id.name)
            self._onchange_selected_lot_id()
        return {'type': 'ir.actions.do_nothing'}

    def action_add_to_order(self):
        """Add selected product/lot to the sales order"""
        self.ensure_one()
        
        if self.quantity <= 0:
            raise UserError(_('Quantity must be greater than zero.'))
        
        line_vals = {
            'order_id': self.sale_order_id.id,
            'product_uom_qty': self.quantity,
        }
        
        if self.search_type == 'product' and self.selected_product_id:
            line_vals['product_id'] = self.selected_product_id.id
            product_name = self.selected_product_id.name

        elif self.search_type in ['lot', 'lot_selection'] and self.selected_lot_id:
            line_vals['product_id'] = self.selected_lot_id.product_id.id
            line_vals['barcode_scan'] = self.selected_lot_id.name  # Use barcode_scan field to store lot name

            # Add location information if selected
            if self.selected_location_id:
                line_vals['line_location_id'] = self.selected_location_id.id
                product_name = f"{self.selected_lot_id.product_id.name} (Lot: {self.selected_lot_id.name}, Location: {self.selected_location_id.name})"
            else:
                product_name = f"{self.selected_lot_id.product_id.name} (Lot: {self.selected_lot_id.name})"

        else:
            raise UserError(_('Please select a product or lot to add.'))

        # Create the order line
        new_line = self.env['sale.order.line'].create(line_vals)

        # Trigger onchange to set proper values
        try:
            new_line._onchange_product_id()
        except Exception:
            pass

        # Clear the search field in the parent order
        self.sale_order_id.order_product_search = ''

        # Log success
        _logger.info("Successfully created order line with product: %s (Qty: %.2f)", product_name, self.quantity)

        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def action_cancel(self):
        """Cancel and clear search field"""
        self.sale_order_id.order_product_search = ''
        return {'type': 'ir.actions.act_window_close'}
