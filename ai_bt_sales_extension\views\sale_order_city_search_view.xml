<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enhanced Sale Order Search View with Customer City Search -->
        <record id="view_order_filter_city_search" model="ir.ui.view">
            <field name="name">sale.order.search.city</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_sales_order_filter"/>
            <field name="arch" type="xml">
                <!-- Add customer city search field -->
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="partner_city"/>
                </xpath>
            </field>
        </record>



        <!-- Add Customer City column to Sale Order List View -->
        <record id="view_order_tree_city_column" model="ir.ui.view">
            <field name="name">sale.order.tree.city.column</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_tree"/>
            <field name="arch" type="xml">
                <!-- Add customer city column after partner_id -->
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="partner_city" optional="hide"/>
                </xpath>
            </field>
        </record>

        <!-- Add Customer City column to Sale Quotation List View -->
        <record id="view_quotation_tree_city_column" model="ir.ui.view">
            <field name="name">sale.quotation.tree.city.column</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_quotation_tree"/>
            <field name="arch" type="xml">
                <!-- Add customer city column after partner_id -->
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="partner_city" optional="hide"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
