<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Product Lot Selection Wizard - Inherit from existing barcode module -->
    <record id="view_product_lot_selection_wizard_form_enhanced" model="ir.ui.view">
        <field name="name">product.lot.selection.wizard.form.enhanced</field>
        <field name="model">product.lot.selection.wizard</field>
        <field name="inherit_id" ref="barcode_scanning_sale_purchase.view_product_lot_selection_wizard_form"/>
        <field name="arch" type="xml">
            <!-- Add weight information to the wizard -->
            <xpath expr="//field[@name='selected_product_id']" position="after">
                <group string="Weight Information" col="2">
                    <field name="selected_lot_weight" readonly="1"/>
                    <field name="selected_lot_total_qty" readonly="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Enhanced Product Selection Wizard from barcode_scanning_sale_purchase -->
    <record id="view_product_selection_wizard_form_enhanced" model="ir.ui.view">
        <field name="name">product.selection.wizard.form.enhanced</field>
        <field name="model">product.selection.wizard</field>
        <field name="inherit_id" ref="barcode_scanning_sale_purchase.view_product_selection_wizard_form"/>
        <field name="arch" type="xml">
            <!-- Add weight display to product selection -->
            <xpath expr="//field[@name='product_lines']" position="attributes">
                <attribute name="context">{'show_weight_info': True}</attribute>
            </xpath>
        </field>
    </record>

    <!-- Enhanced Product Selection Wizard Line List -->
    <record id="view_product_selection_wizard_line_tree_enhanced" model="ir.ui.view">
        <field name="name">product.selection.wizard.line.tree.enhanced</field>
        <field name="model">product.selection.wizard.line</field>
        <field name="arch" type="xml">
            <list string="Products">
                <field name="product_id"/>
                <field name="product_code"/>
                <field name="product_name"/>
                <field name="product_uom"/>
                <field name="total_qty_available"/>
                <field name="total_weight_available" string="Total Weight (kg)"/>
                <field name="stock_details_text" string="Stock by Location"/>
                <field name="is_selected"/>
            </list>
        </field>
    </record>
</odoo>
