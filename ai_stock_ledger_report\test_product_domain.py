#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to check product domain and availability
Run this in Odoo shell: python odoo-bin shell -d your_database
"""

def test_product_domain():
    """Test product domain and availability"""
    print("🔍 Testing product domain and availability...")
    
    # Test 1: All products
    all_products = env['product.product'].search([])
    print(f"✅ Total products in system: {len(all_products)}")
    
    # Test 2: Active products only
    active_products = env['product.product'].search([('active', '=', True)])
    print(f"✅ Active products: {len(active_products)}")
    
    # Test 3: Product type 'product'
    product_type_products = env['product.product'].search([('type', '=', 'product')])
    print(f"✅ Products with type 'product': {len(product_type_products)}")
    
    # Test 4: Product type 'consu'
    consu_type_products = env['product.product'].search([('type', '=', 'consu')])
    print(f"✅ Products with type 'consu': {len(consu_type_products)}")
    
    # Test 5: Our domain filter
    domain_products = env['product.product'].search([
        ('type', 'in', ['product', 'consu']), 
        ('active', '=', True)
    ])
    print(f"✅ Products matching our domain: {len(domain_products)}")
    
    # Test 6: Show sample products
    if domain_products:
        print("\n📋 Sample products:")
        for product in domain_products[:5]:
            print(f"  - {product.name} [{product.default_code or 'No Code'}] (Type: {product.type})")
    else:
        print("❌ No products found matching domain!")
        print("\n🔧 Suggestions:")
        print("1. Create some products in Inventory > Products")
        print("2. Make sure products are active")
        print("3. Set product type to 'Storable Product' or 'Consumable'")
    
    # Test 7: Check product templates
    templates = env['product.template'].search([('active', '=', True)])
    print(f"\n✅ Product templates: {len(templates)}")
    
    return len(domain_products) > 0

if __name__ == "__main__":
    # This would be run in Odoo shell context
    print("Run this script in Odoo shell:")
    print("python odoo-bin shell -d your_database")
    print("Then execute: exec(open('ai_stock_ledger_report/test_product_domain.py').read())")
