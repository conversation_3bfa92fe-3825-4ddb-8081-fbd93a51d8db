{
    'name': 'AI Sale Duplicate Product Location',
    'version': '********.0',
    'category': 'Sales',
    'summary': 'Allow duplicate product-location combinations in sale order lines',
    'description': """
        This module removes constraints that prevent adding the same product 
        with the same location to the same sale order. This is useful when 
        you need to sell the same product at different prices due to different 
        manufacturing costs or other business requirements.
        
        Features:
        - Removes SQL constraints preventing duplicate product-location combinations
        - Allows multiple sale order lines with same product and location
        - Supports different prices for same product due to manufacturing costs
    """,
    'author': 'AI Assistant',
    'depends': ['sale', 'stock'],
    'data': [],
    'installable': True,
    'auto_install': False,
    'application': False,
}
