<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Stock Lot List View -->
    <record id="view_production_lot_tree_enhanced" model="ir.ui.view">
        <field name="name">stock.lot.tree.enhanced</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_tree"/>
        <field name="arch" type="xml">
            <field name="create_date" position="before">
                <field name="display_weight_info" string="Weight Info" optional="show"/>
                <field name="total_available_qty" string="Total Available" optional="show"/>
            </field>
        </field>
    </record>

    <!-- Enhanced Stock Lot Form View -->
    <record id="view_production_lot_form_enhanced" model="ir.ui.view">
        <field name="name">stock.lot.form.enhanced</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <group>
                    <field name="total_available_qty" readonly="1"/>
                    <field name="display_weight_info" readonly="1"/>
                </group>
            </xpath>
        </field>
    </record>



    <!-- Enhanced Stock Quant List View -->
    <record id="view_stock_quant_tree_enhanced" model="ir.ui.view">
        <field name="name">stock.quant.tree.enhanced</field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.view_stock_quant_tree_editable"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='lot_id']" position="after">
                <field name="lot_weight_display" string="Weight Info" optional="show"/>
            </xpath>
        </field>
    </record>

    <!-- Lot Selection with Weight Action -->
    <record id="action_lot_selection_with_weight" model="ir.actions.act_window">
        <field name="name">Lot Selection with Weight</field>
        <field name="res_model">stock.lot</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('total_available_qty', '>', 0)]</field>
        <field name="help">Select lots with available stock and weight information</field>
    </record>
</odoo>
