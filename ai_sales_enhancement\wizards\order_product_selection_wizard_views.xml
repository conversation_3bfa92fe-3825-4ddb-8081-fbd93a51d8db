<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Order Product Selection Wizard Form View -->
    <record id="view_order_product_selection_wizard_form" model="ir.ui.view">
        <field name="name">order.product.selection.wizard.form</field>
        <field name="model">order.product.selection.wizard</field>
        <field name="arch" type="xml">
            <form string="Select Product to Add">
                <group>
                    <field name="search_term" readonly="1"/>
                    <field name="search_type" readonly="1"/>
                </group>
                
                <!-- Product Selection Section -->
                <group string="Product Selection" invisible="search_type != 'product'">
                    <field name="product_ids" invisible="1"/>
                    <field name="selected_product_id"
                           options="{'no_create': True, 'no_edit': True}"
                           placeholder="Select a product from search results..."/>
                    <field name="quantity"/>
                </group>

                <!-- Weight Information Section -->
                <group string="Weight Information" col="4" invisible="not selected_product_id and not selected_lot_id">
                    <field name="product_weight" string="Unit Weight (kg)" readonly="1"/>
                    <field name="total_weight" string="Total Weight (kg)" readonly="1"/>
                    <field name="available_weight" string="Available Weight (kg)" readonly="1"/>
                </group>
                
                <!-- Lot Selection Section -->
                <group string="Lot Selection" invisible="search_type not in ['lot', 'lot_selection']">
                    <field name="lot_ids" invisible="1"/>
                    <field name="selected_lot_id"
                           options="{'no_create': True, 'no_edit': True}"
                           placeholder="Select a lot from search results..."/>
                    <div class="o_row" invisible="not selected_lot_id">
                        <field name="selected_location_id"
                               options="{'no_create': True, 'no_edit': True}"
                               class="oe_inline"/>
                        <button name="action_refresh_locations" string="Refresh" type="object"
                                class="btn-secondary oe_inline"
                                help="Refresh available locations for selected lot"/>
                    </div>
                    <field name="quantity"/>
                </group>
                
                <!-- Product Information -->
                <group string="Product Information">
                    <field name="product_info" readonly="1" nolabel="1"/>
                </group>
                
                <!-- Available Locations (for lots) -->
                <group string="Available Locations" invisible="search_type not in ['lot', 'lot_selection'] or not selected_lot_id">
                    <field name="available_locations" readonly="1" nolabel="1"/>
                </group>
                
                <footer>
                    <button string="Add to Order" 
                            name="action_add_to_order" 
                            type="object" 
                            class="btn-primary"
                            invisible="not selected_product_id and not selected_lot_id"/>
                    <button string="Cancel" 
                            name="action_cancel" 
                            type="object" 
                            class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action for Order Product Selection Wizard -->
    <record id="action_order_product_selection_wizard" model="ir.actions.act_window">
        <field name="name">Select Product</field>
        <field name="res_model">order.product.selection.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
