<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Configuration Settings -->
    <record id="res_config_settings_view_form_enhanced" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.enhanced</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="stock.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='stock_operations']" position="after">
                <div class="col-12 col-lg-6 o_setting_box" id="ai_sales_enhancement_settings">
                    <div class="o_setting_left_pane">
                        <field name="enable_enhanced_product_search"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="enable_enhanced_product_search"/>
                        <div class="text-muted">
                            Enable enhanced product/lot search functionality in sales orders
                        </div>
                        <div class="content-group" invisible="not enable_enhanced_product_search">
                            <div class="mt16">
                                <field name="auto_fill_location_from_lot" class="o_light_label"/>
                                <label for="auto_fill_location_from_lot"/>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 col-lg-6 o_setting_box" id="negative_stock_settings">
                    <div class="o_setting_left_pane">
                        <field name="allow_negative_stock_sales"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="allow_negative_stock_sales"/>
                        <div class="text-muted">
                            Allow sales of products even when stock is negative (useful for made-to-order)
                        </div>
                        <div class="content-group" invisible="not allow_negative_stock_sales">
                            <div class="mt16">
                                <field name="auto_create_lots_negative_stock" class="o_light_label"/>
                                <label for="auto_create_lots_negative_stock"/>
                            </div>
                            <div class="mt16">
                                <label for="default_lot_prefix" class="o_light_label"/>
                                <field name="default_lot_prefix" placeholder="LOT"/>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-12 col-lg-6 o_setting_box" id="truck_tracking_settings">
                    <div class="o_setting_left_pane">
                        <field name="enable_truck_tracking"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="enable_truck_tracking"/>
                        <div class="text-muted">
                            Enable truck number tracking for delivery orders
                        </div>
                        <div class="content-group" invisible="not enable_truck_tracking">
                            <div class="mt16">
                                <field name="mandatory_truck_number" class="o_light_label"/>
                                <label for="mandatory_truck_number"/>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
