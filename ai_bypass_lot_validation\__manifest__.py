{
    'name': 'AI Bypass Lot Validation',
    'version': '********.0',
    'category': 'Inventory',
    'summary': 'Temporarily bypass lot-product compatibility validation',
    'description': """
        This module temporarily bypasses the lot-product compatibility validation
        in stock move lines to allow using lots with different products.
        
        WARNING: This is a temporary workaround and should be used with caution.
        It disables an important data integrity check.
    """,
    'author': 'AI Assistant',
    'depends': ['stock'],
    'data': [],
    'installable': True,
    'auto_install': False,
    'application': False,
}
