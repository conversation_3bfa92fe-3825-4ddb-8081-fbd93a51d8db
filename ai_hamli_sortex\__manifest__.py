{
    'name': 'AI Hamli Sortex Rate Editor',
    'version': '********.0',
    'category': 'Manufacturing',
    'summary': 'Edit Hamli and Sortex rates with cost calculation as rate × weight',
    'description': """
        AI Hamli Sortex Rate Editor
        ============================

        This module allows users to edit hamli and sortex rates in any manufacturing
        order state with costs calculated as rate × total weight. It is especially
        useful for adjusting rates after manufacturing is completed.

        Features:
        ---------
        * Edit hamli rates manually in any manufacturing order state
        * Edit sortex rates manually in any manufacturing order state
        * Rate-based cost calculation: Final Cost = Rate × Total Weight (kg)
        * Post-production rate adjustment capability
        * Override automatic rate calculations with manual rates
        * Recalculate dependent costs (CI landed cost, total cost) based on edited rates
        * Switch between automatic and manual rate calculation modes
        * Maintain audit trail of rate changes

        The module extends the existing hamli and sortex rate functionality from
        ai_bt_spices_module while preserving backward compatibility.
    """,
    'author': 'AI Assistant',
    'website': '',
    'depends': [
        'base',
        'mrp',
        'ai_bt_spices_module',
    ],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'views/mrp_production_views.xml',
    ],
    'test': [
        'tests/test_hamli_sortex_override.py',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}
