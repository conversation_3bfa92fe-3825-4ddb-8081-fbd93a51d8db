# Installation Guide: Multiple Product Lines in Sales Orders

## Overview
This guide will help you install and test the updated `ai_bt_sales_extension` module that allows multiple sale order lines with the same product but different rates.

## Prerequisites
- Odoo 18 Community Edition
- Access to Odoo Apps/Modules management
- Administrator privileges

## Installation Steps

### Step 1: Update the Module
Since the module is already installed, you need to upgrade it to get the new functionality:

1. **Access Apps Menu**:
   - Go to `Apps` in the main menu
   - Remove the "Apps" filter to show installed modules
   - Search for "AI BT Sales Extension"

2. **Upgrade the Module**:
   - Click on the module
   - Click "Upgrade" button
   - Wait for the upgrade to complete

### Step 2: Alternative Command Line Upgrade
If you have command line access:

```bash
# Navigate to your Odoo directory
cd /path/to/your/odoo

# Upgrade the module
python odoo-bin -u ai_bt_sales_extension -d your_database_name --stop-after-init

# Or restart Odoo with upgrade
python odoo-bin -u ai_bt_sales_extension -d your_database_name
```

### Step 3: Verify Installation
1. **Check Module Version**:
   - Go to `Apps` → Search "AI BT Sales Extension"
   - Verify version shows `1.1.0`

2. **Check Field Visibility**:
   - Go to `Sales` → `Orders` → Create new order
   - Add order lines and check if "Line #" column is visible
   - The field should be optional but visible

## Testing the Functionality

### Test 1: Basic Multiple Product Lines

1. **Create a Sale Order**:
   - Go to `Sales` → `Orders` → `Create`
   - Select a customer

2. **Add First Product Line**:
   - Add a product (e.g., Rice)
   - Set quantity: 10
   - Set product rate: 100
   - Note the Line # should be 10

3. **Add Second Line with Same Product**:
   - Add the same product again
   - Set quantity: 5
   - Set product rate: 120 (different rate)
   - Note the Line # should be 20

4. **Add Third Line with Same Product**:
   - Add the same product again
   - Set quantity: 8
   - Set product rate: 90 (different rate)
   - Note the Line # should be 30

### Test 2: Verify Line Sequences

1. **Check Line Numbers**:
   - All lines should have unique sequence numbers (10, 20, 30, etc.)
   - Lines with the same product should be distinguishable

2. **Save and Confirm**:
   - Save the order
   - Confirm the order
   - Verify all lines are preserved

### Test 3: Invoice Generation

1. **Create Invoice**:
   - From the confirmed sale order, create invoice
   - Check that invoice lines are created properly
   - Lines with sequence > 10 should have "(Line X)" in description

## Troubleshooting

### Issue: "Field line_sequence does not exist"
**Solution**: 
- Ensure the module is properly upgraded
- Clear browser cache
- Restart Odoo server if needed

### Issue: Line # column not visible
**Solution**:
- In the sale order line list view, click the "⚙️" (settings) icon
- Enable the "Line #" column
- Or use the optional field controls

### Issue: Sequences not incrementing
**Solution**:
- Check that both model files are properly loaded
- Verify the create method is working
- Check Odoo logs for any errors

### Issue: Module upgrade fails
**Solution**:
- Check Odoo logs for specific errors
- Ensure all dependencies are satisfied
- Try upgrading in safe mode

## Advanced Testing Script

Use the provided test script for comprehensive testing:

```bash
# In Odoo shell
python odoo-bin shell -d your_database_name

# Run the test
exec(open('test_multiple_product_lines.py').read())
```

## Expected Results

After successful installation and testing:

✅ **Multiple Product Lines**: Same product can be added multiple times  
✅ **Different Rates**: Each line can have different rates/prices  
✅ **Automatic Sequencing**: Lines numbered 10, 20, 30, etc.  
✅ **Visual Identification**: "Line #" column visible  
✅ **Invoice Integration**: Sequences carry through to invoices  
✅ **Backward Compatibility**: Existing orders work normally  

## Support

If you encounter any issues:

1. **Check Logs**: Look at Odoo server logs for error details
2. **Verify Dependencies**: Ensure all required modules are installed
3. **Test Environment**: Try in a test database first
4. **Documentation**: Refer to `MULTIPLE_PRODUCT_LINES_IMPLEMENTATION.md`

## Next Steps

Once installed and tested:

1. **Train Users**: Show users how to use the new functionality
2. **Update Procedures**: Update your sales procedures if needed
3. **Monitor Performance**: Check that the changes don't impact performance
4. **Backup**: Ensure you have recent backups before going live

## Rollback Plan

If you need to rollback:

1. **Restore Database**: From backup before upgrade
2. **Reinstall Previous Version**: If available
3. **Remove Custom Fields**: Manually if needed

The implementation is designed to be backward compatible, so rollback should not be necessary in most cases.
