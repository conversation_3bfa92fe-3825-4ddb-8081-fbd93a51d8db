# Changelog

## Version 18.0.1.0.0 (Initial Release)

### Features
- ✅ Stock ledger report with Excel export
- ✅ Date range filtering
- ✅ Warehouse filtering (required)
- ✅ Optional product filtering
- ✅ Lot/serial number tracking
- ✅ Product-wise grouping with totals
- ✅ Running balance calculations
- ✅ Professional Excel formatting
- ✅ Voucher number and account tracking
- ✅ Rate and amount calculations
- ✅ Simplified interface (no redundant location filter)

### Report Columns
- DATE: Transaction date
- VOUCHER NO.: Reference/picking number
- ACCOUNT: Source/destination location
- LOT NUMBER: Lot/serial numbers
- QTY REC: Quantity received
- QTY ISS: Quantity issued
- QTY BAL: Running quantity balance
- UNITS: Unit of measure
- PER KG COST: Cost per unit
- AMOUNT: Total amount
- UNIT ISS: Units issued
- RATE: Rate/price
- AMOUNT: Amount calculation
- REMARK: Additional remarks
- UNITS BAL: Units balance

### Technical Details
- Compatible with Odoo 18.0
- Requires: stock, stock_account, purchase, sale modules
- Uses xlsxwriter for Excel generation
- Responsive web interface
- Proper security access controls

### Installation
1. Copy module to addons directory
2. Update apps list
3. Install "AI Stock Ledger Report" module
4. Access via Inventory > Reporting > Stock Ledger Report

### Interface Improvements
- Removed redundant location filter (warehouse selection automatically includes all warehouse locations)
- Simplified two-field interface: warehouses (required) + products (optional)
- Better user experience with clear field labels and help text

### Known Limitations
- Only shows moves in 'done' state
- Requires at least one warehouse selection
- Excel export requires xlsxwriter package

### Future Enhancements
- PDF export option
- Additional filtering options
- Custom date formats
- Multi-currency support
- Batch processing for large datasets
