<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Security Group for Hamli Sortex Cost Editors -->
        <record id="group_hamli_sortex_cost_editor" model="res.groups">
            <field name="name">Hamli Sortex Cost Editor</field>
            <field name="category_id" ref="base.module_category_manufacturing"/>
            <field name="comment">Users in this group can edit hamli and sortex costs after manufacturing completion</field>
        </record>
        
        <!-- Security Group for Hamli Sortex Cost Managers -->
        <record id="group_hamli_sortex_cost_manager" model="res.groups">
            <field name="name">Hamli Sortex Cost Manager</field>
            <field name="category_id" ref="base.module_category_manufacturing"/>
            <field name="implied_ids" eval="[(4, ref('group_hamli_sortex_cost_editor'))]"/>
            <field name="comment">Users in this group can manage all hamli and sortex cost overrides and view audit trails</field>
        </record>

    </data>
</odoo>
