from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)


class QuickLotCreationWizard(models.TransientModel):
    _name = 'quick.lot.creation.wizard'
    _description = 'Quick Lot Creation Wizard for Delivery Notes'

    picking_id = fields.Many2one(
        'stock.picking',
        string='Delivery Order',
        required=True
    )
    
    move_line_ids = fields.Many2many(
        'stock.move.line',
        string='Move Lines'
    )
    
    line_ids = fields.One2many(
        'quick.lot.creation.wizard.line',
        'wizard_id',
        string='Products'
    )
    
    auto_generate_lot_names = fields.Boolean(
        string='Auto-generate Lot Names',
        default=True,
        help="Automatically generate lot names based on product and date"
    )

    @api.model
    def default_get(self, fields_list):
        """Set default values"""
        res = super().default_get(fields_list)
        
        picking_id = self.env.context.get('default_picking_id')
        move_line_ids = self.env.context.get('default_move_line_ids', [])
        
        if picking_id and move_line_ids:
            if isinstance(move_line_ids[0], tuple):
                move_line_ids = move_line_ids[0][2]
            
            lines = []
            for move_line_id in move_line_ids:
                move_line = self.env['stock.move.line'].browse(move_line_id)
                if move_line.product_id.tracking in ['lot', 'serial']:
                    # Generate default lot name
                    default_lot_name = self._generate_lot_name(move_line.product_id)
                    
                    lines.append((0, 0, {
                        'move_line_id': move_line.id,
                        'product_id': move_line.product_id.id,
                        'quantity': move_line.qty_done or move_line.product_uom_qty,
                        'new_lot_name': default_lot_name,
                        'create_new_lot': True,
                    }))
            
            res['line_ids'] = lines
        
        return res

    def _generate_lot_name(self, product):
        """Generate default lot name"""
        # Get default prefix from settings or product
        prefix = self.env['ir.config_parameter'].sudo().get_param(
            'ai_sales_enhancement.default_lot_prefix', 'LOT'
        )
        
        if product.default_lot_prefix:
            prefix = product.default_lot_prefix
        
        # Generate unique lot name
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        base_name = f"{prefix}-{product.default_code or product.name[:10]}-{timestamp}"
        
        # Ensure uniqueness
        counter = 1
        lot_name = base_name
        while self.env['stock.lot'].search([('name', '=', lot_name)]):
            lot_name = f"{base_name}-{counter}"
            counter += 1
        
        return lot_name

    def action_create_lots(self):
        """Create lots and assign to move lines"""
        created_lots = []
        
        for line in self.line_ids:
            if line.create_new_lot and line.new_lot_name:
                # Check if lot already exists
                existing_lot = self.env['stock.lot'].search([
                    ('name', '=', line.new_lot_name),
                    ('product_id', '=', line.product_id.id)
                ])
                
                if existing_lot:
                    lot = existing_lot
                else:
                    # Create new lot
                    lot = self.env['stock.lot'].create({
                        'name': line.new_lot_name,
                        'product_id': line.product_id.id,
                        'company_id': self.picking_id.company_id.id,
                    })
                    created_lots.append(lot)
                
                # Assign lot to move line
                line.move_line_id.write({
                    'lot_id': lot.id,
                    'qty_done': line.quantity,
                })
                
                _logger.info(f"Assigned lot {lot.name} to move line {line.move_line_id.id}")
            
            elif line.existing_lot_id:
                # Use existing lot
                line.move_line_id.write({
                    'lot_id': line.existing_lot_id.id,
                    'qty_done': line.quantity,
                })
        
        # Show success message
        if created_lots:
            lot_names = ', '.join(created_lots.mapped('name'))
            message = _('Successfully created lots: %s') % lot_names
        else:
            message = _('Lots assigned successfully')
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': message,
                'type': 'success',
            }
        }

    @api.onchange('auto_generate_lot_names')
    def _onchange_auto_generate_lot_names(self):
        """Auto-generate lot names when option is enabled"""
        if self.auto_generate_lot_names:
            for line in self.line_ids:
                if not line.new_lot_name:
                    line.new_lot_name = self._generate_lot_name(line.product_id)


class QuickLotCreationWizardLine(models.TransientModel):
    _name = 'quick.lot.creation.wizard.line'
    _description = 'Quick Lot Creation Wizard Line'

    wizard_id = fields.Many2one(
        'quick.lot.creation.wizard',
        string='Wizard',
        required=True,
        ondelete='cascade'
    )
    
    move_line_id = fields.Many2one(
        'stock.move.line',
        string='Move Line',
        required=True
    )
    
    product_id = fields.Many2one(
        'product.product',
        string='Product',
        required=True
    )
    
    quantity = fields.Float(
        string='Quantity',
        required=True
    )

    # Weight information fields
    product_weight = fields.Float(
        string='Unit Weight (kg)',
        related='product_id.weight',
        readonly=True,
        help="Weight per unit of the product"
    )

    total_weight = fields.Float(
        string='Total Weight (kg)',
        compute='_compute_total_weight',
        help="Total weight based on quantity and unit weight"
    )

    create_new_lot = fields.Boolean(
        string='Create New Lot',
        default=True
    )
    
    new_lot_name = fields.Char(
        string='New Lot Name',
        help="Name for the new lot to be created"
    )
    
    existing_lot_id = fields.Many2one(
        'stock.lot',
        string='Existing Lot',
        domain="[('product_id', '=', product_id)]",
        help="Select existing lot instead of creating new one"
    )
    
    available_lots = fields.Text(
        string='Available Lots',
        compute='_compute_available_lots'
    )

    @api.depends('product_id')
    def _compute_available_lots(self):
        """Compute available lots for the product"""
        for line in self:
            if line.product_id:
                lots = self.env['stock.lot'].search([
                    ('product_id', '=', line.product_id.id)
                ])
                
                lot_info = []
                for lot in lots:
                    quants = lot.quant_ids.filtered(lambda q: q.quantity > 0)
                    total_qty = sum(quants.mapped('quantity'))
                    if total_qty > 0:
                        weight_info = f" ({lot.weight:.2f} kg)" if lot.weight else ""
                        lot_info.append(f"{lot.name}: {total_qty:.2f}{weight_info}")
                
                line.available_lots = "\n".join(lot_info) if lot_info else "No existing lots with stock"
            else:
                line.available_lots = ""

    @api.depends('quantity', 'product_id.weight')
    def _compute_total_weight(self):
        """Compute total weight based on quantity and product weight"""
        for line in self:
            if line.product_id and line.product_id.weight:
                line.total_weight = line.quantity * line.product_id.weight
            else:
                line.total_weight = 0.0

    @api.onchange('create_new_lot')
    def _onchange_create_new_lot(self):
        """Clear fields when switching between new/existing lot"""
        if self.create_new_lot:
            self.existing_lot_id = False
            if not self.new_lot_name:
                self.new_lot_name = self.wizard_id._generate_lot_name(self.product_id)
        else:
            self.new_lot_name = False

    @api.onchange('existing_lot_id')
    def _onchange_existing_lot_id(self):
        """Update create_new_lot when existing lot is selected"""
        if self.existing_lot_id:
            self.create_new_lot = False
            self.new_lot_name = False
