from odoo import api, models, _
import logging

_logger = logging.getLogger(__name__)


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    @api.constrains('lot_id', 'product_id')
    def _check_lot_product(self):
        """
        Override the core lot-product compatibility validation.
        
        ORIGINAL CONSTRAINT:
        This constraint validates that line.lot_id.product_id == line.product_id
        
        BYPASS LOGIC:
        - Log warnings instead of raising ValidationError
        - Allow incompatible lot-product combinations
        - Add context flag to completely disable if needed
        """
        
        # Check if bypass is completely disabled via context
        if self.env.context.get('bypass_lot_validation_disabled', False):
            # Call the original validation
            return super()._check_lot_product()
        
        # Check if bypass is enabled via context (default: True)
        bypass_enabled = self.env.context.get('bypass_lot_validation', True)
        
        if not bypass_enabled:
            # Call the original validation
            return super()._check_lot_product()
        
        # Bypass mode: Log warnings instead of raising errors
        for line in self:
            if line.lot_id and line.product_id != line.lot_id.sudo().product_id:
                _logger.warning(
                    "LOT_VALIDATION_BYPASS: Lot '%s' (Product: %s) is being used with "
                    "different product '%s' in move line %s. This bypasses the standard validation.",
                    line.lot_id.name,
                    line.lot_id.product_id.name,
                    line.product_id.display_name,
                    line.id
                )
                
                # Optional: You can add additional logic here
                # For example, auto-correct the lot's product:
                # if self.env.context.get('auto_correct_lot_product', False):
                #     line.lot_id.product_id = line.product_id
                #     _logger.info("Auto-corrected lot %s to product %s", 
                #                  line.lot_id.name, line.product_id.name)
        
        # Return without raising ValidationError
        return True

    def write(self, vals):
        """
        Override write to add bypass context when updating lot_id
        """
        # Add bypass context when writing lot_id
        if 'lot_id' in vals:
            self = self.with_context(bypass_lot_validation=True)
        
        return super().write(vals)

    @api.model
    def create(self, vals):
        """
        Override create to add bypass context when creating with lot_id
        """
        # Add bypass context when creating with lot_id
        if 'lot_id' in vals:
            self = self.with_context(bypass_lot_validation=True)
        
        return super().create(vals)

    def action_enable_lot_validation(self):
        """
        Action to re-enable lot validation for specific records
        """
        self.with_context(bypass_lot_validation_disabled=True)._check_lot_product()
        return True

    def action_disable_lot_validation(self):
        """
        Action to disable lot validation for specific records
        """
        # This method exists for completeness but bypass is enabled by default
        return True
