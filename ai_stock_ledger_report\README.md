# AI Stock Ledger Report

## Overview
This module provides a comprehensive stock ledger report that generates detailed Excel reports showing product-wise stock movements with lot tracking information.

## Features
- **Date Range Filtering**: Select specific date ranges for the report
- **Warehouse Filtering**: Filter by specific warehouses (required)
- **Product Filtering**: Optional filtering by specific products
- **Lot Tracking**: Shows lot numbers for tracked products
- **Excel Export**: Generates professional Excel reports

## Report Format
The report includes the following columns:
- DATE: Transaction date
- VOUCHER NO./ACCOUNT: Combined reference number and account information
- NAME: Additional remarks/notes (moved next to voucher)
- LOT NUMBER: Lot/serial numbers for tracked products
- QTY REC: Quantity received (number of units)
- QTY ISS: Quantity issued (number of units)
- QTY BAL: Running quantity balance (number of units)
- WEIGHT REC: Weight received (qty × product weight in kg)
- WEIGHT ISS: Weight issued (qty × product weight in kg)
- WEIGHT BAL: Running weight balance (total weight in kg)
- UNITS: Unit of measure
- PER KG COST: Cost per kilogram
- AMOUNT: Total amount
- UNIT ISS: Units issued
- RATE: Rate/price per unit
- AMOUNT: Amount calculation
- UNITS BAL: Units balance

## Usage
1. Go to Inventory > Reporting > Stock Ledger Report
2. Select the date range
3. Choose warehouses (required)
4. Optionally filter by specific products
5. Click "Export to Excel" to generate the report

## Installation
1. Copy the module to your Odoo addons directory
2. Update the app list
3. Install the "AI Stock Ledger Report" module

## Dependencies
- stock
- stock_account
- purchase
- sale

## Author
AI Assistant

## License
LGPL-3
