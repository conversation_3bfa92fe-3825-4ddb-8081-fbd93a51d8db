<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Lot Location Selection Wizard Form View -->
    <record id="view_lot_location_selection_wizard_form" model="ir.ui.view">
        <field name="name">lot.location.selection.wizard.form</field>
        <field name="model">lot.location.selection.wizard</field>
        <field name="arch" type="xml">
            <form string="Select Lot Numbers and Locations">
                <header>
                    <button name="action_confirm" string="Confirm Selection" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="sale_order_id" readonly="1"/>
                        </h1>
                        <h3>Select lot numbers and locations for tracked products</h3>
                    </div>
                    
                    <notebook>
                        <page string="Product Lines">
                            <field name="line_ids">
                                <list editable="bottom">
                                    <field name="product_id" readonly="1"/>
                                    <field name="quantity_needed" readonly="1"/>
                                    <field name="selected_lot_id" 
                                           domain="[('product_id', '=', product_id)]"
                                           options="{'no_create': True}"
                                           required="1"/>
                                    <field name="selected_location_id" 
                                           domain="[('usage', '=', 'internal')]"
                                           options="{'no_create': True}"
                                           required="1"/>
                                    <field name="available_qty_at_location" readonly="1"/>
                                    <field name="lot_weight" readonly="1"/>
                                </list>
                                <form string="Product Line">
                                    <group>
                                        <group>
                                            <field name="product_id" readonly="1"/>
                                            <field name="quantity_needed" readonly="1"/>
                                        </group>
                                        <group>
                                            <field name="selected_lot_id" 
                                                   domain="[('product_id', '=', product_id)]"
                                                   options="{'no_create': True}"/>
                                            <field name="selected_location_id" 
                                                   domain="[('usage', '=', 'internal')]"
                                                   options="{'no_create': True}"/>
                                        </group>
                                    </group>
                                    
                                    <group string="Stock Information">
                                        <field name="available_qty_at_location" readonly="1"/>
                                        <field name="lot_weight" readonly="1"/>
                                    </group>
                                    
                                    <group string="Available Lots">
                                        <field name="available_lots" readonly="1" nolabel="1"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action for Lot Location Selection Wizard -->
    <record id="action_lot_location_selection_wizard" model="ir.actions.act_window">
        <field name="name">Select Lot Numbers and Locations</field>
        <field name="res_model">lot.location.selection.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
