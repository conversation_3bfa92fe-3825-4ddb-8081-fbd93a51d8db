#!/bin/bash
echo "=== RUNNING LOT COMPATIBILITY FIX SCRIPT ==="
echo "This script will help you run the fix_lot_compatibility.py script in Odoo shell"
echo ""

# Check if we're in the right directory
if [ ! -f "fix_lot_compatibility.py" ]; then
    echo "❌ ERROR: fix_lot_compatibility.py not found in current directory"
    echo "Please navigate to /mnt/extra-addons first"
    exit 1
fi

echo "✅ Found fix_lot_compatibility.py"
echo ""

echo "STEP 1: Access Odoo Shell"
echo "Run one of these commands depending on your setup:"
echo ""
echo "For Docker setup:"
echo "docker exec -it <odoo_container_name> odoo shell -d <database_name>"
echo ""
echo "For direct installation:"
echo "python3 /usr/bin/odoo shell -d <database_name>"
echo "OR"
echo "python3 odoo-bin shell -d <database_name>"
echo ""

echo "STEP 2: Once in Odoo shell, run these commands:"
echo ""
echo "# Load the script"
echo "exec(open('/mnt/extra-addons/fix_lot_compatibility.py').read())"
echo ""
echo "# Run the main fix function"
echo "fix_lot_compatibility()"
echo ""
echo "# Optional: Check manufacturing order details"
echo "check_manufacturing_order_details()"
echo ""
echo "# Optional: Show alternative fix (risky)"
echo "alternative_fix_reassign_lot()"
echo ""

echo "STEP 3: Copy and paste the commands above into your Odoo shell"
echo ""
echo "=== READY TO RUN ==="
